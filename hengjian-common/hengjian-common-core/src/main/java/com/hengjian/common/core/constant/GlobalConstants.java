package com.hengjian.common.core.constant;

/**
 * 全局的key常量 (业务无关的key)
 *
 * <AUTHOR> Li
 */
public interface GlobalConstants {

    /**
     * 全局 redis key (业务无关的key)
     */
    String GLOBAL_REDIS_KEY = "global:";

    /**
     * 验证码 redis key
     */
    String CAPTCHA_CODE_KEY = GLOBAL_REDIS_KEY + "captcha_codes:";

    /**
     * 防重提交 redis key
     */
    String REPEAT_SUBMIT_KEY = GLOBAL_REDIS_KEY + "repeat_submit:";

    /**
     * 限流 redis key
     */
    String RATE_LIMIT_KEY = GLOBAL_REDIS_KEY + "rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    String PWD_ERR_CNT_KEY = GLOBAL_REDIS_KEY + "pwd_err_cnt:";

    /**
     * 邮件发送限制-每日 redis key
     */
    String EMAIL_DAILY_MAX_KEY = GLOBAL_REDIS_KEY + "email_limit:daily:";

    /**
     * 邮件发送限制-每分钟 redis key
     */
    String EMAIL_MINUTE_MAX_KEY = GLOBAL_REDIS_KEY + "email_limit:minute:";
    /**
     * TEMPORARY_TABLE中间表分布式锁Key
     */
    String TEMPORARY_TABLE_LOCK_KEY = GLOBAL_REDIS_KEY + "lock:temporary_table";
    /**
     * CONF_ZIP
     */
    String CONF_ZIP_LOCK_KEY = GLOBAL_REDIS_KEY + "conf_zip";

    /**
     * SPU收藏
     */
    String FAVORITES_SPU = GLOBAL_REDIS_KEY + "favorites_spu";

    /**
     * 订单附件
     */
    String ORDER_ATTACHMENT= GLOBAL_REDIS_KEY + "order_attachment_";
    /**
     * mysql订单和ES订单校验
     */
    String ORDER_CANAL_CHECK= GLOBAL_REDIS_KEY + "order_canal_check";
    /**
     * 商品代发库存状态 0无库存 1有库存
     */
    String SKU_DROP_SHIPPING_STOCK_STATUS = GLOBAL_REDIS_KEY + "DropShippingStockStatus";
    /**
     * 订单来源
     */
    String ORDER_SOURCE = GLOBAL_REDIS_KEY + "order_source";

}
