package com.zsmall.order.biz.support;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hengjian.common.core.enums.LanguageType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.system.domain.SysTenant;
import com.hengjian.system.domain.TenantSpecifiedDisplay;
import com.hengjian.system.domain.vo.SysOssVo;
import com.hengjian.system.mapper.SysConfigMapper;
import com.hengjian.system.service.ISysConfigService;
import com.hengjian.system.service.ISysTenantService;
import com.hengjian.system.service.impl.SysOssServiceImpl;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.calculate.entity.util.DeliveryFeeV2Utils;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.domain.DeliveryFeeByErpResponse;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.stock.AdjustStockDTO;
import com.zsmall.common.domain.vo.IntactAddressInfoVo;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.common.CarrierTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderItem.ShippingOrderStateEnum;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.common.enums.worldLocation.LocationTypeEnum;
import com.zsmall.common.exception.*;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.UnitConverter;
import com.zsmall.extend.utils.ZSMallActivityEventUtils;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.lottery.support.PriceBussinessV2Support;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.order.biz.factory.ChannelOrderInspectionFactory;
import com.zsmall.order.biz.service.IProductAboutManger;
import com.zsmall.order.biz.service.OrderItemPriceService;
import com.zsmall.order.biz.service.OrderItemService;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.order.OrderUpdateBo;
import com.zsmall.order.entity.domain.dto.*;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.biz.service.ProductSkuStockService;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAdjustStockVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.biz.service.TenantWalletService;
import com.zsmall.system.entity.domain.*;
import com.zsmall.system.entity.iservice.*;
import com.zsmall.system.entity.mapper.TenantWalletMapper;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.event.ThirdWarehouseEvent;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zsmall.common.enums.order.OrderFlowEnum.THIRD_CREATE_ORDER;

/**
 * 订单相关支持类
 *
 * <AUTHOR>
 * @date 2023/6/9
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderSupport {
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;
    private final DeliveryFeeV2Utils deliveryFeeUtils;
    private final OrderItemService orderItemService;
    private final OrderItemPriceService orderItemPriceService;
    private final IProductSkuStockService iProductSkuStockService;
    private final RuleLevelProductPriceV2Service ruleLevelProductPriceService;
    private final TenantWalletMapper tenantWalletMapper;
    private final SysConfigMapper sysConfigMapper;
    private final ISysConfigService configService;

    private final PriceBussinessV2Support priceBussinessSupport;
//    循环以来
//    private final OrdersService ordersService;
    private final ITransactionRecordService iTransactionRecordService;
    private final IOrderAttachmentService iOrderAttachmentService;
    private final IProductAboutManger productAboutManger;
    private final IOrdersService iOrdersService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IWorldLocationService iWorldLocationService;
    private final IProductSkuPriceRuleRelationService iProductSkuPriceRuleRelationService;
    private final IWarehouseService iWarehouseService;
    private final IOrderImportTempService iOrderImportTempService;
    private final IOrderItemShippingRecordService iOrderItemShippingRecordService;
    private final IOrderRefundService iOrderRefundService;
    private final ITransactionsOrdersService iTransactionsOrdersService;
    private final IProductChannelControlService iProductChannelControlService;
//    private final IProductActivityPriceItemService iProductActivityPriceItemService;
//    private final IProductActivityItemService iProductActivityItemService;
//    private final IProductActivityStockItemService iProductActivityStockItemService;

    private final ProductSupport productSupport;
    private final ProductSkuStockService productSkuStockService;
    private final TenantWalletService tenantWalletService;

    private final OrderCodeGenerator orderCodeGenerator;
    private final MallSystemCodeGenerator mallSystemCodeGenerator;
    private final BusinessParameterService businessParameterService;
    private final ThirdPartyLogisticsSupport thirdPartyLogisticsSupport;
    private final ChannelOrderInspectionFactory channelOrderInspectionFactory;
    private final ITransactionsWholesaleIntentionOrderService iTransactionsWholesaleIntentionOrderService;
    private final IProductSkuService productSkuService;
    private final IProductService productService;
    private final IProductMappingService iProductMappingService;
    private final PriceSupportV2 priceSupportV2;
    private final RabbitTemplate rabbitTemplate;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final SysOssServiceImpl sysOssService;
    private final IChannelWarehouseInfoServiceImpl iChannelWarehouseService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final OrderActivitySupport orderActivitySupport;
//    private final DistributorOrderService distributorOrderService;

    @Autowired
    private ISysTenantService sysTenantService;

    /**
     * 订单支付链,错误信息不可抛出
     *
     * @param orderList
     * @param isNeedCreate
     * @param isGenuinePay
     */
    public void orderPayChain(String tenantId, List<Orders> orderList, boolean isNeedCreate,
                              boolean isGenuinePay) throws StockException, OrderPayException, ProductException {
        // 活动订单校验
       orderActivitySupport.checkActivityException(orderList);
        // 检查订单是否有商品未映射
        orderStatusCheck(orderList);
        // 检测订单是否上传标签
        isExpressSheetUpload(tenantId, orderList);
        if(CollUtil.isEmpty(orderList)){
            return;
        }
        List<Orders> pendingOrderList = orderLockToPending(tenantId, orderList);
        // 会重新计算价格
        orderPreInspection(pendingOrderList);
        orderStockAdjust(pendingOrderList);
        // 钱包支付,但是一旦后续流程发生异常,钱包金额目前并没有回滚
        orderWalletPay(pendingOrderList, isGenuinePay);
        orderUpdateLogistics(pendingOrderList);
        orderThirdWarehouseFollowUp(pendingOrderList, isNeedCreate);
    }

    @InMethodLog("订单支付链队列")
    public void orderPayChainQueue(String tenantId, List<Orders> orderList, boolean isNeedCreate,
                              boolean isGenuinePay) throws StockException, OrderPayException, ProductException {
        // 活动订单校验
        orderActivitySupport.checkActivityException(orderList);
        // 校验订单的交易记录
        orderTransactionCheck(orderList);
        // 检查订单是否有商品未映射
        orderStatusCheck(orderList);
        // 检测订单是否上传标签
        isExpressSheetUpload(tenantId, orderList);
        if(CollUtil.isEmpty(orderList)){
            return;
        }
        // 重新计算价格
        orderPreInspection(orderList);
        orderStockAdjust(orderList);
        // 钱包支付,但是一旦后续流程发生异常,钱包金额目前并没有回滚
        orderWalletPay(orderList, isGenuinePay);
        orderUpdateLogistics(orderList);
        for(Orders orders : orderList){
            String str = JSON.toJSONString(orders);
            String messageId = IdUtil.fastSimpleUUID();
            Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
            log.info("[订单推送erp队列]成功，发送参数：{}",JSON.toJSON(orders));
            rabbitTemplate.convertAndSend(RabbitMqConstant.DISTRIBUTION_ORDER_EXCHANGE,RabbitMqConstant.DISTRIBUTION_PUSH_ORDER_ROUTING_KEY,message);
        }
    }

    /**
     * 第三方订单支付检查
     *
     * @param tenantId
     * @param orderList
     * @throws StockException
     */
    public void orderPayForThirdCheck(String tenantId, List<Orders> orderList) throws StockException, ProductException {
        // 活动订单校验
        orderActivitySupport.checkActivityException(orderList);
        // 检查订单是否有商品未映射
        orderStatusCheck(orderList);
        // 检测订单是否上传标签
        isExpressSheetUpload(tenantId, orderList);
        if(CollUtil.isEmpty(orderList)){
            return;
        }
        List<Orders> pendingOrderList = orderLockToPending(tenantId, orderList);
        orderPreInspection(pendingOrderList);
        orderStockAdjust(pendingOrderList);
    }

    /**
     * 第三方订单支付
     *
     * @param tenantId
     * @param orderList
     * @param isNeedCreate
     * @param isGenuinePay
     * @throws OrderPayException
     */
    public void orderPayForThird(String tenantId, List<Orders> orderList, boolean isNeedCreate,
                              boolean isGenuinePay) throws OrderPayException {
        // 将非pending状态的订单状态修改为pendind状态的订单
        orderLockToPendingForThird(tenantId, orderList);
        // 钱包支付,但是一旦后续流程发生异常,钱包金额目前并没有回滚
        orderWalletPay(orderList, isGenuinePay);
        orderUpdateLogistics(orderList);
        orderThirdWarehouseFollowUp(orderList, isNeedCreate);
    }

    /**
     * 第三方订单支付失败处理
     *
     * @param orders
     * @param msg
     */
    public void orderPayFailThird(List<Orders> orders, String msg){
        if (CollUtil.isNotEmpty(orders)){
            for(Orders order : orders){
                order.setOrderState(OrderStateType.Failed);
                order.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_PAY_ERROR_MESSAGE.args(msg)).toJSON());
                iOrdersService.updateById(order);
                iOrderItemService.changeOrderStateByOrderId(order.getId(), OrderStateType.Pending, order.getOrderState());
                if (OrderStateType.Failed.equals(order.getOrderState())) {
                    List<OrderItem> orderItemList = iOrderItemService.getListByOrderId(order.getId());
                    if(CollUtil.isNotEmpty(orderItemList)){
                        // 归还已占用的库存
                        orderItemRestock(orderItemList);
                    }
                }
            }
        }
    }

    public void orderPayChainNotToErp(String tenantId, List<Orders> orderList, boolean isNeedCreate,
                                      boolean isGenuinePay) throws StockException, OrderPayException {
        // 活动订单校验
        orderActivitySupport.checkActivityException(orderList);
        // 检测订单是否上传标签
        isExpressSheetUpload(tenantId, orderList);
        if(CollUtil.isEmpty(orderList)){
            return;
        }
        List<Orders> pendingOrderList = orderLockToPending(tenantId, orderList);
        orderPreInspection(pendingOrderList);
        orderStockAdjust(pendingOrderList);
        // 钱包支付,但是一旦后续流程发生异常,钱包金额目前并没有回滚
        orderWalletPay(pendingOrderList, isGenuinePay);
        orderUpdateLogistics(pendingOrderList);
//        orderThirdWarehouseFollowUp(pendingOrderList, isNeedCreate);

    }

    public void orderPayChainNotToErpV3(String tenantId, List<Orders> orderList, boolean isNeedCreate,
                                      boolean isGenuinePay) throws StockException, OrderPayException, ProductException {
        // 活动订单校验
        orderActivitySupport.checkActivityException(orderList);
        // 检查订单是否有商品未映射
        orderStatusCheck(orderList);
        // 检测订单是否上传标签
        isExpressSheetUpload(tenantId, orderList);
        if(CollUtil.isEmpty(orderList)){
            return;
        }
        List<Orders> pendingOrderList = orderLockToPending(tenantId, orderList);
        orderPreInspection(pendingOrderList);
        orderStockAdjust(pendingOrderList);
        // 钱包支付,但是一旦后续流程发生异常,钱包金额目前并没有回滚
        orderWalletPay(pendingOrderList, isGenuinePay);
        orderUpdateLogistics(pendingOrderList);
//        orderThirdWarehouseFollowUp(pendingOrderList, isNeedCreate);

    }


    /**
     * 功能描述：推送至第三仓库
     *
     * <AUTHOR>
     * @date 2024/03/01
     */
    public void pushToThirdWareHouseFollowUp() {
        List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.list(new LambdaUpdateWrapper<Orders>()
            .eq(Orders::getFulfillmentProgress, LogisticsProgress.Abnormal)));
        TenantHelper.ignore(() -> orderThirdWarehouseFollowUp(ordersList, false));
    }

    ;

    /**
     * 功能描述：面单是否上传
     *
     * @param tenantId  租户id
     * @param orderList 订单列表
     * <AUTHOR>
     * @date 2024/03/01
     */
    private void isExpressSheetUpload(String tenantId, List<Orders> orderList) {
        List<Orders> noUploadOrder = new ArrayList<>();
        for (Orders order : orderList) {
            OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(order.getOrderNo());
            if(ObjectUtil.isEmpty(orderLogisticsInfo)){
                log.info("订单异常或未进行映射:{}", com.alibaba.fastjson.JSONObject.toJSONString(order.getOrderNo()));
                continue;
            }
            String logisticsAccount = orderLogisticsInfo.getLogisticsAccount();
            LogisticsTypeEnum logisticsType = order.getLogisticsType();
            if (!CarrierTypeEnum.LTL.getValue().equals(orderLogisticsInfo.getLogisticsCarrierCode())){
                if (LogisticsTypeEnum.PickUp.equals(logisticsType) && ChannelTypeEnum.TikTok.equals(order.getChannelType()) &&1==order.getOrderSource()) {
                    if (StrUtil.isEmpty(logisticsAccount)) {
                        OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(order.getOrderNo(), OrderAttachmentTypeEnum.ShippingLabel);
                        if (ObjectUtil.isEmpty(orderAttachment)) {
                            log.info("订单{}未上传快递标签", order.getOrderNo());
//                        throw new RStatusCodeException(ZSMallStatusCodeEnum.TEMP_ORDER_PICK_UP_NOT_EXIST_SHIPPING_LABEL_ERROR);
                            LocaleMessage localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.TEMP_ORDER_PICK_UP_NOT_EXIST_SHIPPING_LABEL_ERROR);
                            order.setOrderState(OrderStateType.Failed);
                            order.setPayErrorMessage(localeMessage.toJSON());
                            order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                            noUploadOrder.add(order);
                        }
                    }
                }
                if (LogisticsTypeEnum.PickUp.equals(logisticsType) && ChannelTypeEnum.Temu.equals(order.getChannelType())) {
                    if (StrUtil.isEmpty(logisticsAccount)) {
                        OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(order.getOrderNo(), OrderAttachmentTypeEnum.ShippingLabel);
                        if (ObjectUtil.isEmpty(orderAttachment)) {
                            log.info("订单{}未上传快递标签", order.getOrderNo());
                            LocaleMessage localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.TEMP_ORDER_PICK_UP_NOT_EXIST_SHIPPING_LABEL_ERROR);
                            order.setOrderState(OrderStateType.Failed);
                            order.setPayErrorMessage(localeMessage.toJSON());
                            order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                            noUploadOrder.add(order);
                        }
                    }
                }
                if (LogisticsTypeEnum.PickUp.equals(logisticsType) && ChannelTypeEnum.Amazon_VC.equals(order.getChannelType())) {
                    if (StrUtil.isEmpty(logisticsAccount)) {
                        OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(order.getOrderNo(), OrderAttachmentTypeEnum.ShippingLabel);
                        if (ObjectUtil.isEmpty(orderAttachment)) {
                            log.info("订单{}未上传快递标签", order.getOrderNo());
                            LocaleMessage localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.TEMP_ORDER_PICK_UP_NOT_EXIST_SHIPPING_LABEL_ERROR);
                            order.setOrderState(OrderStateType.Failed);
                            order.setPayErrorMessage(localeMessage.toJSON());
                            order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                            noUploadOrder.add(order);
                        }
                    }
                }
                if (LogisticsTypeEnum.PickUp.equals(logisticsType) && ChannelTypeEnum.EC.equals(order.getChannelType())) {
                    if (StrUtil.isEmpty(logisticsAccount)) {
                        OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(order.getOrderNo(), OrderAttachmentTypeEnum.ShippingLabel);
                        if (ObjectUtil.isEmpty(orderAttachment)) {
                            log.info("订单{}未上传快递标签", order.getOrderNo());
                            LocaleMessage localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.TEMP_ORDER_PICK_UP_NOT_EXIST_SHIPPING_LABEL_ERROR);
                            order.setOrderState(OrderStateType.Failed);
                            order.setPayErrorMessage(localeMessage.toJSON());
                            order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                            noUploadOrder.add(order);
                        }
                    }
                }
            }

            iOrdersService.updateById(order);
        }
        orderList.removeAll(noUploadOrder);
    }

    /**
     * 功能描述：判断是否上传面单
     * @param orderList
     * @return
     */
    public Boolean isExpressSheetUpload(List<Orders> orderList) {
        for (Orders order : orderList) {
            OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(order.getOrderNo());
            if(ObjectUtil.isEmpty(orderLogisticsInfo)){
                log.info("订单异常或未进行映射:{}", com.alibaba.fastjson.JSONObject.toJSONString(order.getOrderNo()));
                return false;
            }
            String logisticsAccount = orderLogisticsInfo.getLogisticsAccount();
            LogisticsTypeEnum logisticsType = order.getLogisticsType();
            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                if (StrUtil.isEmpty(logisticsAccount)) {
                    OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(order.getOrderNo(), OrderAttachmentTypeEnum.ShippingLabel);
                    if (ObjectUtil.isEmpty(orderAttachment)) {
                       return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     *
     */
    public List<Orders> orderLockToPending(String tenantId, List<Orders> orderList) throws RStatusCodeException {
        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_ORDER_PAY_LOCK + tenantId;

        RLock rLock = client.getLock(key);
        rLock.lock(10, TimeUnit.MINUTES);

        List<Orders> newOrderList = new ArrayList<>();
        List<String> collect = orderList.stream().map(Orders::getOrderNo).collect(Collectors.toList());

        try {
            log.info("订单状态变更，开锁成功 key = {}", key);
            for (Orders order : orderList) {
                String orderNo = order.getOrderNo();
                OrderStateType orderState = order.getOrderState();

                if (OrderStateType.UnPaid.equals(orderState) || OrderStateType.Failed.equals(orderState)) {
                    Orders newOrder = iOrdersService.queryByOrderNoAndStateNotTenant(orderNo, orderState);
                    if (newOrder != null) {
                        newOrder.setOrderState(OrderStateType.Pending);
                        newOrderList.add(newOrder);
                    }
                }
            }

            if (CollUtil.size(newOrderList) == CollUtil.size(orderList)) {
                iOrdersService.updateBatchById(newOrderList);
                for (Orders order : newOrderList) {
                    iOrderItemService.changeOrderStateByOrderId(order.getId(), null, OrderStateType.Pending);
                }
            } else {

                throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_HAS_CHANGED);
            }
        } catch (RStatusCodeException e) {
            for (Orders order : newOrderList) {
                iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getId, order.getId())
                                                                       .set(Orders::getOrderState, OrderStateType.Failed));
                iOrderItemService.changeOrderStateByOrderId(order.getId(), null, OrderStateType.Failed);
            }
            log.info("订单状态变更出现异常（RStatusCodeException） {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            for (Orders order : newOrderList) {
                iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getId, order.getId())
                                                                       .set(Orders::getOrderState, OrderStateType.Failed));
                iOrderItemService.changeOrderStateByOrderId(order.getId(), null, OrderStateType.Failed);
            }
            log.info("订单状态变更出现异常 {}", e.getMessage(), e);
            throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_EXCEPTION);
        } finally {
            rLock.unlock();
            log.info("订单状态变更，解锁成功 key = {}", key);
        }
        return newOrderList;
    }

    /**
     * 加锁修改订单状态
     * @param tenantId
     * @param orderList
     * @return
     * @throws RStatusCodeException
     */
    public List<Orders> orderLockToPendingUpdate(String tenantId, List<Orders> orderList) throws RStatusCodeException {
        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_ORDER_PAY_LOCK + tenantId;
        log.info("订单状态变更，加锁 key = {}，订单 = {}", key,JSONUtil.toJsonStr(orderList));
        RLock rLock = client.getLock(key);
        rLock.lock(10, TimeUnit.MINUTES);
        List<Orders> newOrderList = new ArrayList<>();
        List<String> orderNoList = orderList.stream().map(Orders::getOrderNo).collect(Collectors.toList());
        try {
            // 只处理待支付和支付失败的订单
            newOrderList = TenantHelper.ignore(() -> iOrdersService.queryByOrderNoListAndStateIn(orderNoList, OrderStateType.UnPaid, OrderStateType.Failed));
            if (CollUtil.size(newOrderList) == CollUtil.size(orderList)) {
                log.info("订单状态变更订单信息：{}", JSONUtil.toJsonStr(newOrderList));
                // 修改订单状态
                for(Orders newOrder : newOrderList){
                    newOrder.setOrderState(OrderStateType.Pending);
                }
                iOrdersService.updateBatchById(newOrderList);
                List<Long> orderIdList = orderList.stream().map(Orders::getId).collect(Collectors.toList());
                // 修改订单子表订单状态
                iOrderItemService.changeOrderStateByOrderId(orderIdList,OrderStateType.Pending);
            } else {

                throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_HAS_CHANGED);
            }
        } catch (RStatusCodeException e) {
            for (Orders order : newOrderList) {
                iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getId, order.getId())
                                                                       .set(Orders::getOrderState, OrderStateType.Failed));
                iOrderItemService.changeOrderStateByOrderId(order.getId(), null, OrderStateType.Failed);
            }
            log.info("订单状态变更出现异常（RStatusCodeException） {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            for (Orders order : newOrderList) {
                iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getId, order.getId())
                                                                       .set(Orders::getOrderState, OrderStateType.Failed));
                iOrderItemService.changeOrderStateByOrderId(order.getId(), null, OrderStateType.Failed);
            }
            log.info("订单状态变更出现异常 {}", e.getMessage(), e);
            throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_EXCEPTION);
        } finally {
            rLock.unlock();
            log.info("订单状态变更，解锁成功 key = {}", key);
        }
        return newOrderList;
    }

    /**
     * 第三方订单支付，状态判断
     *
     * @param tenantId
     * @param orderList
     * @throws RStatusCodeException
     */
    public void orderLockToPendingForThird(String tenantId, List<Orders> orderList) throws RStatusCodeException {
        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_ORDER_PAY_LOCK + tenantId;
        RLock rLock = client.getLock(key);
        rLock.lock(10, TimeUnit.MINUTES);
        List<Orders> newOrderList = new ArrayList<>();
        try {
            log.info("订单状态变更，开锁成功 key = {}", key);
            for (Orders order : orderList) {
                String orderNo = order.getOrderNo();
                OrderStateType orderState = order.getOrderState();

                if (!OrderStateType.Pending.equals(orderState)) {
                    log.warn("订单：{} 的状态发生变化",order);
                    Orders newOrder = iOrdersService.queryByOrderNoAndStateNotTenant(orderNo, orderState);
                    if (newOrder != null) {
                        newOrder.setOrderState(OrderStateType.Pending);
                        newOrderList.add(newOrder);
                    }
                }
            }
            if (CollUtil.isNotEmpty(newOrderList)) {
                iOrdersService.updateBatchById(newOrderList);
                for (Orders order : newOrderList) {
                    iOrderItemService.changeOrderStateByOrderId(order.getId(), null, OrderStateType.Pending);
                }
            }
        } catch (RStatusCodeException e) {
            for (Orders order : newOrderList) {
                iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getId, order.getId())
                                                                       .set(Orders::getOrderState, OrderStateType.Failed));
                iOrderItemService.changeOrderStateByOrderId(order.getId(), null, OrderStateType.Failed);
            }
            log.info("订单状态变更出现异常（RStatusCodeException） {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            for (Orders order : newOrderList) {
                iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getId, order.getId())
                                                                       .set(Orders::getOrderState, OrderStateType.Failed));
                iOrderItemService.changeOrderStateByOrderId(order.getId(), null, OrderStateType.Failed);
            }
            log.info("订单状态变更出现异常 {}", e.getMessage(), e);
            throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_EXCEPTION);
        } finally {
            rLock.unlock();
            log.info("订单状态变更，解锁成功 key = {}", key);
        }
    }

    /**
     * 订单前置检查
     *
     * @param orderList
     */
    @SneakyThrows
    private void orderPreInspection(List<Orders> orderList) {
//        log.info("准备进行订单前置检查 orderList.size = {}", CollUtil.size(orderList));
        // 商品管控提示
        String productControlTips = businessParameterService.getValueFromString(BusinessParameterType.PRODUCT_CONTROL_TIPS);
        AtomicInteger count = new AtomicInteger(0);
        StringBuilder errorTips = new StringBuilder();
        ArrayList<String> orders = new ArrayList<>();
        ConcurrentHashMap<String, List<Object>> orderAboutMap = new ConcurrentHashMap<>();
        OrderExceptionEnum  orderExceptionEnum  = OrderExceptionEnum.normal;
        List<OrderLogisticsInfo> logisticsInfos = new ArrayList<>();
        List<OrderItemProductSku> productSkus = new ArrayList<>();
        for (Orders order : orderList) {
            OrderItemPrice orderItemPrice = new OrderItemPrice();
            Long orderId = order.getId();
            String tenantId = order.getTenantId();
            String orderNo = order.getOrderNo();
            OrderStateType orderState = order.getOrderState();
            LogisticsTypeEnum logisticsType = order.getLogisticsType();
            OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderId(orderId);
            OrderItemProductSku one = iOrderItemProductSkuService.getOne(new LambdaQueryWrapper<OrderItemProductSku>().eq(OrderItemProductSku::getOrderNo, order.getOrderNo()));
            String zipCode = null;
            // 拿到所有对应的仓库
            String logisticsCompanyName = null;

            if(ObjectUtil.isNotEmpty(orderLogisticsInfo)){
                logisticsCompanyName = orderLogisticsInfo.getLogisticsCompanyName();
            }
            if(ObjectUtil.isNotEmpty(orderLogisticsInfo)){
                zipCode = orderLogisticsInfo.getZipCode();
            }            // 不是Pending状态的订单，直接跳过
            if (!OrderStateType.Pending.equals(orderState)) {
                continue;
            }

            List<OrderItemPrice> orderItemPriceList = new ArrayList<>();
            List<OrderItem> orderItemList = iOrderItemService.getListByOrderId(orderId);
            // 从订单拿,如果没有就全部查一次
            HashMap<String, List<String>> stashMap = new HashMap<>();
            if(ObjectUtil.isNotEmpty(one)){
                String warehouseSystemCode = one.getWarehouseSystemCode();
                Warehouse warehouse = TenantHelper.ignore(()->iWarehouseService.getOne(new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getWarehouseSystemCode, warehouseSystemCode))) ;
                if (ObjectUtil.isNotEmpty(warehouse)){
                    stashMap.put(one.getOrderItemNo(), CollUtil.newArrayList(warehouse.getWarehouseCode()));
                }else {
                    stashMap = getStashList(orderItemList);
                }
            }else {
                stashMap = getStashList(orderItemList);
            }

            try {
                // 自提且非第三方的订单，需要检查是否上传了快递标签
                if (LogisticsTypeEnum.PickUp.equals(logisticsType) && StrUtil.isBlank(orderLogisticsInfo.getLogisticsAccount())) {
                    Boolean shippingLabelExist = orderLogisticsInfo.getShippingLabelExist();
                    LambdaQueryWrapper<OrderAttachment> wrapper = new LambdaQueryWrapper<OrderAttachment>().eq(OrderAttachment::getOrderNo, order.getOrderNo());
                    List<OrderAttachment> attachmentList = iOrderAttachmentService.list(wrapper);
                    Map<OrderAttachmentTypeEnum, List<OrderAttachment>> typeEnumListMap = attachmentList.stream()
                                                                                                        .collect(Collectors.groupingBy(OrderAttachment::getAttachmentType));
                    boolean notEmpty = CollUtil.isNotEmpty(typeEnumListMap);
                    // open-api LTL单-只需要3个 CartonLabel(4),PalletLabel(5),ItemLabel(6) 非LTL单
                    if(StrUtil.isNotEmpty(logisticsCompanyName)&& CarrierTypeEnum.LTL.getValue().equals(logisticsCompanyName)&& OrderSourceEnum.OPEN_API_ORDER.getValue().equals(order.getOrderSource())){
                        if (!shippingLabelExist || !notEmpty || !(typeEnumListMap.containsKey(OrderAttachmentTypeEnum.CartonLabel) && typeEnumListMap.containsKey(OrderAttachmentTypeEnum.PalletLabel) && typeEnumListMap.containsKey(OrderAttachmentTypeEnum.ItemLabel))) {
//                            throw new OrderPayException(ZSMallStatusCodeEnum.TEMP_ORDER_PICK_UP_NOT_EXIST_SHIPPING_LABEL_ERROR);
                        }
                    }
                    if (!shippingLabelExist||!notEmpty||!typeEnumListMap.containsKey(OrderAttachmentTypeEnum.ShippingLabel) &&!CarrierTypeEnum.LTL.getValue().equals(logisticsCompanyName)&& !OrderSourceEnum.OPEN_API_ORDER.getValue().equals(order.getOrderSource())) {
//                        throw new OrderPayException(ZSMallStatusCodeEnum.TEMP_ORDER_PICK_UP_NOT_EXIST_SHIPPING_LABEL_ERROR);
                    }
                }


                if (ObjectUtil.isEmpty(order.getChannelType())) {
                    order.setChannelType(ChannelTypeEnum.Others);
                }
                ChannelTypeEnum channelType = order.getChannelType();

                OrderAddressInfo addressInfo = iOrderAddressInfoService.getByOrderNoAndAddressType(orderNo, OrderAddressType.ShipAddress);

//                ChannelOrderInspectionService inspectionService = channelOrderInspectionFactory.getService(channelType);
//                if (inspectionService != null) {
//                    inspectionService.logisticsInspection(order, orderItemList, orderLogisticsInfo);
//                }

                // 目的地国家
                String destCountryCode = addressInfo.getCountryCode();
                List<String> controlProduct = new ArrayList<>();


                for (OrderItem orderItem : orderItemList) {
                    String activityCode = orderItem.getActivityCode();
                    String productSkuCode = orderItem.getProductSkuCode();

                    // 商品是否下架判断
                    Product product = iProductService.queryByProductSkuCode(productSkuCode);
                    if(null != product && null != product.getShelfState() && product.getShelfState().equals(ShelfStateEnum.OffShelf)){
                        throw new OrderPayException(ZSMallStatusCodeEnum.PRODUCT_DELISTED);
                    }
                    // 排除erp
                    if(!ChannelTypeEnum.Erp.equals(order.getChannelType())){
                        if(null != order.getOrderSource() && order.getOrderSource().equals(OrderSourceEnum.INTERFACE_ORDER.getValue())){
                            // 商品是否映射判断，只有渠道订单需要判断
//                            ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncState(orderItem.getTenantId(), order.getChannelId(), orderItem.getChannelSku(), SyncStateEnum.Mapped);
                            ProductMapping productMapping = TenantHelper.ignore(()->iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(orderItem.getTenantId(), order.getChannelId(), orderItem.getChannelSku(), SyncStateEnum.Mapped,order.getCountryCode()));
                            if(ObjectUtil.isEmpty(productMapping)){
                                throw new OrderPayException(ZSMallStatusCodeEnum.PRODUCT_MAPPING_EXCEPTION);
                            }
                        }
                    }

                    // 校验商品有效性
                    ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                    if (productSku == null) {
                        throw new OrderPayException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXISTS.args(productSkuCode));
                    }

                    OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemIdNoTenant(orderItem.getId());

                    // 不存在价格规则关联，则提示错误
                    ProductSkuPriceRuleRelation ruleRelation =
                        iProductSkuPriceRuleRelationService.getByProductSkuCode(productSkuCode);
                    if (ruleRelation == null && StrUtil.isBlank(activityCode)) {
                        log.error("商品{}不存在价格规则关联", productSkuCode);
//                        throw new OrderPayException(ZSMallStatusCodeEnum.SYSTEM_ERROR_E10028);
                    }

                    // 判断商品是否受管控
                    boolean allow = iProductChannelControlService.checkUserAllow(tenantId, productSkuCode, channelType.name());
                    if (!allow) {
                        controlProduct.add(productSkuCode);
                        continue;
                    }

                    // 代发才需要判断收货地与发货地是否支持物流 x 测试完成后需要放开
                    if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                     //   this.shippingCountrySupport(activityCode, destCountryCode, productSkuCode, orderItemProductSku.getSpecifyWarehouse());
                    }

                    OrderPriceCalculateDTO dto = new OrderPriceCalculateDTO();
                    dto.setChannelTypeEnum(channelType);
                    dto.setLogisticsType(logisticsType);
                    dto.setOrderItem(orderItem);
                    dto.setCountry(destCountryCode);
                    dto.setActivityCode(activityCode);
                    //  支付完后会根据商品金额进行金额的重写计算 这里需要换成 新priceSupport的逻辑
                    List<String> stashList = stashMap.get(orderItem.getOrderItemNo());
                    LocaleMessage localeMessage = priceSupportV2.calculationOrderItemPrice(dto,tenantId,zipCode,stashList, order, THIRD_CREATE_ORDER, logisticsCompanyName );

                    if (localeMessage.hasData()) {
                        if(OrderExceptionEnum.out_of_stock_exception.getValue().equals(order.getExceptionCode())){
                            throw new StockException(ZSMallStatusCodeEnum.NO_AVAILABLE_STOCK_FOUND.args(productSkuCode));
                        }
                        if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                            if(OrderExceptionEnum.measurement_anomaly.getValue().equals(order.getExceptionCode())){
                                throw new DeliveryFeeException(ZSMallStatusCodeEnum.MEASUREMENT_ANOMALY.args(productSkuCode));
                            }
                            if(OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(order.getExceptionCode())){
                                throw new DeliveryFeeException(ZSMallStatusCodeEnum.FINAL_DELIVERY_FEE_EXCEPTION.args(productSkuCode));
                            }
                        }

                    } else {
                        //  搜集子订单价格表，等会需要重新统计主订单的总金额
                        orderItemPriceList.add(dto.getOrderItemPrice());
                        // 更新物流和产品的价格

                    }
//                    priceSupport.calculationOrderItemPrice(dto,tenantId,zipCode,stashMap.get(orderItem.getOrderItemNo()),order);
                    String warehouseSystemCode = dto.getWarehouseSystemCode();
                    String logisticsCarrierCode = dto.getLogisticsCarrierCode();
                    String logisticsCode = dto.getLogisticsCode();
                    orderLogisticsInfo.setLogisticsCompanyName(logisticsCarrierCode);
                    orderLogisticsInfo.setLogisticsCarrierCode(logisticsCarrierCode);
                    orderLogisticsInfo.setLogisticsServiceName(logisticsCode);
                    logisticsInfos.add(orderLogisticsInfo);
                    one.setWarehouseSystemCode(warehouseSystemCode);
                    productSkus.add(one);
                }

                if (CollUtil.isNotEmpty(controlProduct)) {
                    String newProductControlTips = StrUtil.replace(productControlTips, "{itemNo}", CollUtil.join(controlProduct, "/"));
                    errorTips.append(newProductControlTips).append(";");
                    throw new OrderPayException(LocaleMessage.parseByJSONStr(newProductControlTips));
                }
//                &&(ChannelTypeEnum.TikTok.equals(order.getChannelType()) || ChannelTypeEnum.Temu.equals(order.getChannelType()) ||ChannelTypeEnum.Open.equals(order.getChannelType()))
                // x 重新计算订单金额
                if ((OrderSourceEnum.INTERFACE_ORDER.getValue().equals(order.getOrderSource())||OrderSourceEnum.OPEN_API_ORDER.getValue().equals(order.getOrderSource()))) {
                    // 重新计算订单金额 可能还是需要的,因为此时可能订单的信息并不完整

//                    priceSupport.recalculateOrderAmount(order,orderItemPriceList);
                } else {
                    priceSupportV2.recalculateOrderAmount(order,orderItemPriceList);
                }
                // 订单的发货仓库不支持配送国家的时候不设置为null
                if(null != order.getPayErrorMessage() && !LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE).toJSON().toString().equals(order.getPayErrorMessage().toString())){
                    order.setPayErrorMessage(null);
                }
                orderLogisticsInfo.setLogisticsErrorMessage(null);
            }catch (DeliveryFeeException e) {
                orders.add(order.getOrderNo());
                orderExceptionEnum = OrderExceptionEnum.final_delivery_fee_exception;
                orderAboutMap.computeIfAbsent(SignalSenderEnum.orderItemPrice.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                             .add(orderItemPrice);
                orderAboutMap.computeIfAbsent(SignalSenderEnum.orderItem.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                             .add(orderItemList.get(0));
                orderAboutMap.computeIfAbsent(SignalSenderEnum.orderLogisticsInfo.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                             .add(orderLogisticsInfo);
                orderAboutMap.computeIfAbsent(SignalSenderEnum.orderItemProductSku.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                             .add(one);
                log.info("订单{}前置检查，出现异常（DeliveryFeeException） {}", orderNo, e.getMessage(), e);
                order.setOrderState(OrderStateType.Failed);
                LocaleMessage localeMessage = e.getLocaleMessage();
                JSONObject errorMsg = localeMessage.toJSON();
                order.setPayErrorMessage(errorMsg);
                order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());

                orderLogisticsInfo.setLogisticsErrorMessage(errorMsg);
                orderAboutMap.computeIfAbsent(SignalSenderEnum.orderLogisticsInfo.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                             .add(orderLogisticsInfo);
                errorTips.append(errorMsg.toString()).append(";");
//                throw new Exception(message.toString());
            }
            catch (StockException e) {
                orders.add(order.getOrderNo());
                log.info("订单{}前置检查，出现异常（StockException） {}", orderNo, e.getMessage(), e);
                order.setOrderState(OrderStateType.Failed);
                JSONObject message = e.getLocaleMessage().toJSON();
                order.setPayErrorMessage(message);
                order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                orderLogisticsInfo.setLogisticsErrorMessage(message);
                errorTips.append(message.toString()).append(";");
//                throw new Exception(message.toString());
            }
            catch (RStatusCodeException e) {
                orders.add(order.getOrderNo());
                log.info("订单{}前置检查，出现异常（RStatusCodeException） {}", orderNo, e.getMessage(), e);
                order.setOrderState(OrderStateType.Failed);
                JSONObject message = LocaleMessage.byStatusCodeToJSON(e.getStatusCode());
                order.setPayErrorMessage(message);
                order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                orderLogisticsInfo.setLogisticsErrorMessage(message);
                errorTips.append(message.toString()).append(";");
//                throw new Exception(message.toString());
            } catch (OrderPayException e) {
                log.info("订单{}前置检查，出现异常（OrderPayException） {}", orderNo, e.getMessage(), e);
                orders.add(order.getOrderNo());
                JSONObject errorMsg = e.getLocaleMessage().toJSON();
                order.setOrderState(OrderStateType.Failed);
                order.setPayErrorMessage(errorMsg);
                order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                orderLogisticsInfo.setLogisticsErrorMessage(errorMsg);
                e.printStackTrace();
                errorTips.append(errorMsg.toString()).append(";");
//                throw new Exception(errorMsg.toString());
            } catch (Exception e) {
                orders.add(order.getOrderNo());
                log.info("订单{}前置检查，出现未知异常 {}", orderNo, e.getMessage(), e);
                JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_PAY_UNKNOWN_ERROR).toJSON();
                order.setOrderState(OrderStateType.Failed);
                order.setPayErrorMessage(errorMsg);
                order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                orderLogisticsInfo.setLogisticsErrorMessage(errorMsg);
                e.printStackTrace();
                // 考虑把throw 去掉
                errorTips.append(errorMsg.toString()).append(";");
//                throw new Exception(errorMsg.toString());
            } finally {
                iOrdersService.updateById(order);
                iOrderItemService.updateBatchById(orderItemList);
                iOrderItemPriceService.updateBatchById(orderItemPriceList);
                iOrderLogisticsInfoService.updateById(orderLogisticsInfo);

                if (OrderStateType.Failed.equals(order.getOrderState())) {
                    iOrderItemService.changeOrderStateByOrderId(orderId, OrderStateType.Pending, OrderStateType.Failed);
                    orderAboutMap.computeIfAbsent(SignalSenderEnum.order.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                                 .add(order);
                }
                // count 自增
                count.getAndIncrement();

            }
        }
        try {
            priceBussinessSupport.recalculatePriceAndUpdate(orderAboutMap,orderExceptionEnum.getValue());

        }catch (Exception e){
            log.error("订单价格清洗出现异常",e);
            if (CollUtil.isNotEmpty(orderAboutMap)){
                List<Orders> ordersList = orderAboutMap.get(SignalSenderEnum.order.name()).stream().map(obj -> (Orders) obj)
                                                   .collect(Collectors.toList());
                ordersList.forEach(order -> {
                    order.setOrderState(OrderStateType.Failed);
                });
                iOrdersService.updateBatchById(ordersList);
            }
        }
        // 此处会导致同期支付的其他订单进入pending状态
        if (count.get() == orderList.size() && ObjectUtil.isNotEmpty(errorTips)) {
            LambdaUpdateWrapper<Orders> set = new LambdaUpdateWrapper<Orders>().in(Orders::getOrderNo, orders)
                                                                               .set(Orders::getOrderState, OrderStateType.Failed);
            iOrdersService.update(set);
            if (orderList.size()==1){
                throw new RuntimeException(errorTips.toString());
            }

        }
    }


    /**
     * 订单库存调整
     *
     * @param orderList
     */
    private void orderStockAdjust(List<Orders> orderList) throws StockException {

        List<String> failOrders = new ArrayList<>();
        boolean result = true;
        StringBuilder orderNos = new StringBuilder();
        ArrayList<String> failOrderList = new ArrayList<>();
        StringBuilder errorTips = new StringBuilder();
        // 如果出现了 3 5 6 的异常,都需要将金额重新计算,并且改变订单状态
        // 该状态 只要是 3 5 6就行,无所谓到底是哪个状态
        OrderExceptionEnum  orderExceptionEnum  = OrderExceptionEnum.normal;
        ConcurrentHashMap<String, List<Object>> orderAboutMap = new ConcurrentHashMap<>();
        Orders orders1 = orderList.get(0);
        String tenantId = orders1.getTenantId();
        Boolean approvedTenant = sysTenantService.getIsApprovedTenant(tenantId, 1);
        for (Orders order : orderList) {

            Long orderId = order.getId();
            String orderNo = order.getOrderNo();
            OrderStateType orderState = order.getOrderState();
            List<OrderItemProductSku> orderItemProductSkuList = new ArrayList<>();
            List<AdjustStockDTO> dtoList = new ArrayList<>();
            List<OrderItem> orderItemList = new ArrayList<>();
            OrderItemPrice orderItemPrice = new OrderItemPrice();
            String countryCode = order.getCountryCode();
            // 拿到承运商
            OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderId(orderId);
            String logisticsCompanyName = logisticsInfo.getLogisticsCarrierCode();

            try {
                log.info("主订单ID{}, 订单{}, 状态{}, 开始调整库存", orderId, orderNo, orderState);
                ChannelTypeEnum channelType = order.getChannelType();

                OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderId(orderId);
                orderItemList = iOrderItemService.getListByOrderId(orderId);

                String logisticsAccount = orderLogisticsInfo.getLogisticsAccount();
                String logisticsZipCode = orderLogisticsInfo.getLogisticsZipCode();
                String zipCode = orderLogisticsInfo.getZipCode();
                String logisticsCountryCode = orderLogisticsInfo.getLogisticsCountryCode();
                LogisticsTypeEnum logisticsType = orderLogisticsInfo.getLogisticsType();

                // 不是Pending状态的订单，直接跳过
                if (!OrderStateType.Pending.equals(orderState)) {
                    continue;
                }

                // 已经调整成功的库存dto集合，出现某一个库存调整失败时，已经调整成功的需要归还库存
                for (OrderItem orderItem : orderItemList) {

                    Long orderItemId = orderItem.getId();
                    String orderItemNo = orderItem.getOrderItemNo();
                    log.info("子订单ID{} 子订单{}开始调整库存", orderItemId, orderItemNo);

                    String activityCode = orderItem.getActivityCode();
                    String productSkuCode = orderItem.getProductSkuCode();
                    Integer totalQuantity = orderItem.getTotalQuantity();
                    OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItemId);
                    ProductSku one = TenantHelper.ignore(()->iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, productSkuCode)));
                    // 指定仓库 拿的是系统仓库号 少跟新了specifyWarehous
                    String warehouseSystemCodeOld = orderItemProductSku.getWarehouseSystemCode();
                    String specifyWarehouse = null;
                    List<OrderItem> finalOrderItemList = orderItemList;
                    // 单仓改造后,查询库存信息逻辑不变,但是如果是dropshipping的订单需要额外判断库存的 drop_shipping_stock_available 字段判断是否可以扣减库存 所以finalOrderItemList要扩展发货类型
                    HashMap<String,List<String> >stashMap = getStashList(finalOrderItemList);
                    List<String> stashList = stashMap.get(orderItem.getOrderItemNo());

                    if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())&&CollUtil.isEmpty(stashList)){
                        order.setExceptionCode(OrderExceptionEnum.measurement_anomaly.getValue());
                        throw new DeliveryFeeException(ZSMallStatusCodeEnum.MEASUREMENT_ANOMALY.args(productSkuCode));
                    }
                    // 通过锁机制统一减库存
//                    if(LogisticsTypeEnum.PickUp.equals(order.getLogisticsType())&&CollUtil.isEmpty(stashList)){
//                        order.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
//                        throw new StockException(ZSMallStatusCodeEnum.OUT_OF_STOCK.args(productSkuCode));
//                    }
                    String warehouseCode = null;
                    if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                        if (approvedTenant){
                            try {
                                DeliveryFeeByErpResponse deliveryFeeFromErp = priceSupportV2.getDeliveryFeeFromErp(stashList, zipCode, productSkuCode, Collections.singletonList(logisticsCompanyName), order.getTenantId(), one.getTenantId(),countryCode ,zipCode );
                                warehouseCode = deliveryFeeFromErp.getOrgWarehouseCode();
//                        warehouseCode = "WEM-EFGA03";
                            }catch (Exception e){
                                if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                                    order.setExceptionCode(OrderExceptionEnum.final_delivery_fee_exception.getValue());
                                    throw new DeliveryFeeException(ZSMallStatusCodeEnum.FINAL_DELIVERY_FEE_EXCEPTION.args(productSkuCode));
                                }
                            }
                            // 如果指定仓库和测算仓库不一致,则以重新测算为准
                            if (StrUtil.isNotBlank(warehouseCode) && StrUtil.isNotBlank(warehouseSystemCodeOld)) {
                                 warehouseSystemCodeOld = updateWarehouseInfo(one.getTenantId(), warehouseCode, orderItemProductSku, warehouseSystemCodeOld);
                            }
                        }else {
                            warehouseCode=stashList.get(0);
                            warehouseSystemCodeOld = updateWarehouseInfo(one.getTenantId(), warehouseCode, orderItemProductSku, warehouseSystemCodeOld);
                        }
                    }

                    AdjustStockDTO dto = new AdjustStockDTO();
                    dto.setProductSkuCode(productSkuCode);
                    dto.setSpecifyWarehouse(warehouseSystemCodeOld);
                    dto.setLogisticsType(logisticsType);
                    dto.setActivityCode(activityCode);
                    // 负数时为减少库存
                    dto.setAdjustQuantity(totalQuantity * -1);
                    dto.setDestCountry(logisticsCountryCode);
                    dto.setDestZipCode(logisticsZipCode);
                    dto.setLogisticsAccount(StrUtil.isNotBlank(logisticsAccount));

                    if (StrUtil.isNotBlank(activityCode)) {
                        String warehouseSystemCode = ZSMallActivityEventUtils.activityStockAdjust(dto);
                        orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);
                        orderItemProductSkuList.add(orderItemProductSku);

                        // 加入调整成功的数组中
                        dtoList.add(dto.setSpecifyWarehouse(warehouseSystemCode));
                    } else {
                        // 通过锁机制统一减库存
                        // 可能会库存不足
                        String warehouseSystemCode = productSkuStockService.adjustStock(dto, orderNo);
                        orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);
                        orderItemProductSkuList.add(orderItemProductSku);

                        // 加入调整成功的数组中
                        dtoList.add(dto.setSpecifyWarehouse(warehouseSystemCode));
                    }
                }
            }catch (DeliveryFeeException e) {
                orderExceptionEnum = OrderExceptionEnum.final_delivery_fee_exception;
                if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                    orderAboutMap.computeIfAbsent(SignalSenderEnum.orderItemPrice.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                                 .add(orderItemPrice);
                    orderAboutMap.computeIfAbsent(SignalSenderEnum.orderItem.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                                 .add(orderItemList.get(0));
                }
                log.info("订单{}库存调整出现异常（DeliveryFeeException） {}", orderNo, e.getMessage(), e);
                LocaleMessage localeMessage = e.getLocaleMessage();
                JSONObject errorMsg = localeMessage.toJSON();
                order.setPayErrorMessage(errorMsg);
                order.setOrderState(OrderStateType.Failed);
                // 如果order 的状态 不是 5 6 就填写 3
                order.setExceptionCode(orderExceptionEnum.getValue());
                result = false;
                failOrders.add(order.getOrderNo());
                errorTips.append("订单库存调整出现异常（DeliveryFeeException）" + orderNo + e.getMessage()).append(";");
            } catch (StockException e) {
                orderExceptionEnum = OrderExceptionEnum.out_of_stock_exception;
                if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                    orderAboutMap.computeIfAbsent(SignalSenderEnum.orderItemPrice.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                                 .add(orderItemPrice);
                    orderAboutMap.computeIfAbsent(SignalSenderEnum.orderItem.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                                 .add(orderItemList.get(0));
                }
                log.info("订单{}库存调整出现异常（StockException） {}", orderNo, e.getMessage(), e);
                e.printStackTrace();
                if(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE).toJSON().toString().equals(e.getLocaleMessage().toJSON().toString())){
                    JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE).toJSON();
                    order.setPayErrorMessage(errorMsg);
                    order.setOrderState(OrderStateType.Failed);
//                    result = false;
                    failOrders.add(order.getOrderNo());
                    errorTips.append("订单库存调整出现异常（StockException）" + orderNo + e.getMessage()).append(";");
                }else {
                    LocaleMessage localeMessage = e.getLocaleMessage();
                    JSONObject errorMsg = localeMessage.toJSON();
                    order.setPayErrorMessage(errorMsg);
                    order.setOrderState(OrderStateType.Failed);
                    // 如果order 的状态 不是 5 6 就填写 3
                    order.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                    result = false;
                    failOrders.add(order.getOrderNo());
                    errorTips.append("订单库存调整出现异常（StockException）" + orderNo + e.getMessage()).append(";");
                }
//                    throw new StockException("订单库存调整出现异常（StockException）" + orderNo + e.getMessage());
            } catch (Exception e) {
                if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                    orderAboutMap.computeIfAbsent(SignalSenderEnum.orderItemPrice.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                                 .add(orderItemPrice);
                    orderAboutMap.computeIfAbsent(SignalSenderEnum.orderItem.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                                 .add(orderItemList.get(0));
                }
                orderExceptionEnum = OrderExceptionEnum.out_of_stock_exception;

                log.info("订单{}库存调整出现未知异常 {}", orderNo, e.getMessage(), e);
                JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_STOCK_ADJUST_UNKNOWN_ERROR)
                                                   .toJSON();
                order.setPayErrorMessage(errorMsg);
                order.setOrderState(OrderStateType.Failed);
                // 如果order 的状态 不是 5 6 就填写 3
                order.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                result = false;
                failOrders.add(order.getOrderNo());
                errorTips.append("订单库存调整出现异常（StockException）" + orderNo + e.getMessage()).append(";");
//                    throw new StockException("订单库存调整出现异常（StockException）" + orderNo + e.getMessage());
            } finally {
                iOrdersService.updateById(order);
                OrderStateType finalState = order.getOrderState();
                if (OrderStateType.Failed.equals(finalState)) {
                    iOrderItemService.changeOrderStateByOrderId(orderId, OrderStateType.Pending, OrderStateType.Failed);
                    // 归还已占用的库存
                    adjustStockDTORestock(dtoList);
                    if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                        orderAboutMap.computeIfAbsent(SignalSenderEnum.order.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                                     .add(order);
                    }

                } else {
                    iOrderItemProductSkuService.updateBatchById(orderItemProductSkuList);
                }
                log.info("主订单ID{} 订单{}库存调整完成，最终状态 {}", orderId, orderNo, finalState);

            }

        }

        // 如果订单状态是库存异常,或者测算异常,或尾程异常,则把订单价格清洗,用于重新计算 影响业务 tk open-api temu 商场下单 导入,此流程出现报错会导致单子进入支付中状态
        try {
            priceBussinessSupport.recalculatePriceAndUpdate(orderAboutMap,orderExceptionEnum.getValue());
        }catch (Exception e){
            log.error("订单价格清洗出现异常",e);
            if (CollUtil.isNotEmpty(orderAboutMap)){
                List<Orders> orders = orderAboutMap.get(SignalSenderEnum.order.name()).stream().map(obj -> (Orders) obj)
                                                 .collect(Collectors.toList());
                orders.forEach(order -> {
                    order.setOrderState(OrderStateType.Failed);
                });
                iOrdersService.updateBatchById(orders);
            }
        }
        if (!result) {
            // 此处出现问题后,实际还是有订单还未支付 处于Pending状态 ,这些订单实际应该流入支付流程 ,所以此处需要筛选
            if (orderList.size() > 1) {
                // orderList中筛选掉failOrders集合中的元素
                orderList = orderList.stream().filter(a -> !failOrders.contains(a.getOrderNo()))
                                     .collect(Collectors.toList());
            } else {
                // 订单的状态集体改为失败
                throw new StockException("订单库存调整出现异常（StockException）,任务中断,异常信息:" + errorTips.toString());
            }

        }
    }

    /**
     * 更新仓库信息
     * @param tenantId 租户ID
     * @param warehouseCode 仓库代码
     * @param orderItemProductSku 订单商品SKU
     * @param oldWarehouseSystemCode 原仓库系统代码
     * @return 更新后的仓库系统代码
     */
    private String updateWarehouseInfo(String tenantId, String warehouseCode, OrderItemProductSku orderItemProductSku, String oldWarehouseSystemCode) {
        if (StrUtil.isBlank(warehouseCode) || StrUtil.isBlank(oldWarehouseSystemCode)) {
            return oldWarehouseSystemCode;
        }
        LambdaQueryWrapper<Warehouse> query = new LambdaQueryWrapper<Warehouse>()
            .eq(Warehouse::getTenantId, tenantId)
            .eq(Warehouse::getWarehouseCode, warehouseCode)
            .eq(Warehouse::getDelFlag, 0);
        Warehouse warehouse = TenantHelper.ignore(() -> iWarehouseService.getOne(query));
        if (warehouse == null) {
            return oldWarehouseSystemCode;
        }
        String newWarehouseSystemCode = warehouse.getWarehouseSystemCode();
        if (!oldWarehouseSystemCode.equals(newWarehouseSystemCode)) {
            orderItemProductSku.setWarehouseSystemCode(newWarehouseSystemCode);
            orderItemProductSku.setSpecifyWarehouse(warehouse.getWarehouseSystemCode());
            iOrderItemProductSkuService.updateById(orderItemProductSku);
            return newWarehouseSystemCode;
        }
        return oldWarehouseSystemCode;
    }

    /**
     * 订单钱包支付
     *
     * @param orderList
     * @param isGenuinePay 是否需要走分销钱包扣款
     */
    private void orderWalletPay(List<Orders> orderList, boolean isGenuinePay) throws OrderPayException {
        log.info("准备进行订单钱包支付 orderList.size = {}", CollUtil.size(orderList));
        boolean result = true;
        for (Orders order : orderList) {
            Long orderId = order.getId();
            String orderNo = order.getOrderNo();
            OrderStateType orderState = order.getOrderState();
            String targetTenantId = order.getTenantId();
            log.info("主订单ID{}, 订单{}, 状态{}, 开始钱包支付", orderId, orderNo, orderState);

            String currency = order.getCurrency();
            String currencySymbol = order.getCurrencySymbol();
            // 不是Pending状态的订单，直接跳过
            if (!OrderStateType.Pending.equals(orderState)) {
                continue;
            }
            List<OrderItem> orderItemList = iOrderItemService.getListByOrderId(orderId);

            try {
                // 主订单的实付总金额 tiktok订单不取这个金额
                BigDecimal platformActualTotalAmount = getDisActualTotalAmount(order);
//                BigDecimal platformActualTotalAmount = order.getPlatformActualTotalAmount();

                // 当前统计一次子订单的所有实付金额
                BigDecimal nowActualTotalAmount = getDisNowActualTotalAmount(orderItemList, order);

                // 两个金额符合，开始扣减
                if (NumberUtil.equals(platformActualTotalAmount, nowActualTotalAmount)) {
                    TransactionRecord transactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
                    transactionRecord.setTransactionType(TransactionTypeEnum.Expenditure);
                    transactionRecord.setTransactionSubType(TransactionSubTypeEnum.OrderPay);
                    transactionRecord.setCurrency(order.getCurrency());
                    transactionRecord.setCurrencySymbol(order.getCurrencySymbol());
                    // 分销平台内实际交易金额
                    transactionRecord.setTransactionAmount(platformActualTotalAmount);
                    LambdaQueryWrapper<TenantWallet> twLqw = new LambdaQueryWrapper<>();
                    String tenantId = order.getTenantId();
                    twLqw.eq(TenantWallet::getTenantId, tenantId);
                    if(StringUtils.isNotBlank(order.getCurrency())){
                        twLqw.eq(TenantWallet::getCurrency, order.getCurrency());
                    }
                    TenantWallet tenantWallet = tenantWalletMapper.selectOne(twLqw);
                    BigDecimal balance = BigDecimal.ZERO;
                    if (ObjectUtil.isNotNull(tenantWallet)) {
                        balance = tenantWallet.getWalletBalance();
                    }
                    transactionRecord.setBeforeBalance(balance);
                    if (isGenuinePay) {
                        transactionRecord.setAfterBalance(balance.subtract(platformActualTotalAmount));
                        transactionRecord.setTransactionState(TransactionStateEnum.Processing);
                    } else {
                        transactionRecord.setAfterBalance(balance);
                        transactionRecord.setTransactionState(TransactionStateEnum.Success);
                    }
                    transactionRecord.setTransactionTime(new Date());
                    iTransactionRecordService.save(transactionRecord);
                    iTransactionsOrdersService.saveRelation(transactionRecord.getId(), orderId);
                    // 内部包含钱包扣减服务和钱包变动记录服务--1.0.2版本中支持空中云汇,需要直接支付不需要走钱包,并且生成交易单
                    tenantWalletService.walletChanges(targetTenantId, transactionRecord, isGenuinePay);

                    order.setOrderState(OrderStateType.Paid);
                    order.setPayType(OrderPayTypeEnum.Wallet.name());
                    order.setExceptionCode(OrderExceptionEnum.normal.getValue());
                    order.setPayTime(new Date());

                } else {  // 两个金额不符合，报错
                    throw new OrderPayException(OrderStatusCodeEnum.ORDER_AMOUNT_NOT_MATCH);
                }
            } catch (WalletException e) {
                log.info("订单{}钱包支付时出现异常（WalletException） {}", orderNo, e.getMessage(), e);
                LocaleMessage localeMessage = e.getLocaleMessage();
                // 钱包更新异常特殊处理
                if(LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.WALLET_UPDATE_FAILURE).toJSON().toString().equals(localeMessage.toJSON().toString())){
                    log.error("订单{}钱包更新异常!!!", orderNo);
                    order.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_PAY_UNKNOWN_ERROR)
                                                          .toJSON());
                }else {
                    order.setPayErrorMessage(localeMessage.toJSON());
                }
                order.setOrderState(OrderStateType.Failed);
                order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                result = false;
//                throw new OrderPayException("钱包支付时出现未知异常" + orderNo + e.getMessage());
            } catch (OrderPayException e) {
                e.printStackTrace();
                log.info("订单{}钱包支付时出现异常（OrderPayException） {}", orderNo, e.getMessage(), e);
                LocaleMessage localeMessage = e.getLocaleMessage();
                order.setOrderState(OrderStateType.Failed);
                order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                order.setPayErrorMessage(localeMessage.toJSON());
                result = false;
//                throw new OrderPayException("钱包支付时出现未知异常" + orderNo + e.getMessage());
            } catch (Exception e) {
                log.info("订单{}钱包支付时出现未知异常 {}", orderNo, e.getMessage(), e);
                order.setOrderState(OrderStateType.Failed);
                order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                order.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_PAY_UNKNOWN_ERROR)
                                                      .toJSON());
                result = false;
//                throw new OrderPayException("钱包支付时出现未知异常" + orderNo + e.getMessage());
            } finally {
                if (result) {
                    order.setChannelReceiptStatus("NotReceipted");
                }

                iOrdersService.updateById(order);
                iOrderItemService.changeOrderStateByOrderId(orderId, OrderStateType.Pending, order.getOrderState());
                if (OrderStateType.Failed.equals(order.getOrderState())) {
                    // 归还已占用的库存
                    orderItemRestock(orderItemList);
                }
            }
            if (!result) {
                throw new OrderPayException("钱包支付时出现异常（OrderPayException）");
            }

        }
    }

    /**
     * 功能描述：立即获取分销 子订单实际总额
     *
     * @param orderItemList 订单项目列表
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/03/01
     */
    private BigDecimal getDisNowActualTotalAmount(List<OrderItem> orderItemList, Orders order) {
        BigDecimal nowActualTotalAmount = BigDecimal.ZERO;

        if(OrderSourceEnum.INTERFACE_ORDER.getValue().equals(order.getOrderSource())||OrderSourceEnum.OPEN_API_ORDER.getValue().equals(order.getOrderSource())){
            if (LogisticsTypeEnum.PickUp.equals(order.getLogisticsType())) {
                return order.getOriginalTotalPickUpPrice();

            }
            if (LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())) {
                return order.getOriginalTotalDropShippingPrice();
            }
            if (LogisticsTypeEnum.ZSMallDropShipping.equals(order.getLogisticsType())) {
                return order.getOriginalTotalDropShippingPrice();
            }
            return nowActualTotalAmount;
        }else {
            for (OrderItem orderItem : orderItemList) {
                nowActualTotalAmount = nowActualTotalAmount.add(orderItem.getOriginalActualTotalAmount());
            }
        }

        return nowActualTotalAmount;
    }

    /**
     * 功能描述：获取dis实际总额
     *
     * @param order 顺序
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/03/01
     */
    private BigDecimal getDisActualTotalAmount(Orders order) {
        if(OrderSourceEnum.INTERFACE_ORDER.getValue().equals(order.getOrderSource())||OrderSourceEnum.OPEN_API_ORDER.getValue().equals(order.getOrderSource())){
            if (LogisticsTypeEnum.PickUp.equals(order.getLogisticsType())) {
                return order.getOriginalTotalPickUpPrice();

            }
            if (LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())) {
                return order.getOriginalTotalDropShippingPrice();
            }
            if (LogisticsTypeEnum.ZSMallDropShipping.equals(order.getLogisticsType())) {
                return order.getOriginalTotalDropShippingPrice();
            }
        }else {
            return order.getPlatformActualTotalAmount();
        }
        return null;
    }

    /**
     * 订单第三方仓库后续操作
     *
     * @param orderList
     * @param isNeedCreate
     */
    public void orderThirdWarehouseFollowUp(List<Orders> orderList, boolean isNeedCreate) {
        log.info("准备进行订单第三方仓库后续操作 orderList.size = {}", CollUtil.size(orderList));
        List<String> tenantIds = orderList.stream().map(Orders::getTenantId).distinct().collect(Collectors.toList());
        Map<String, SysTenant> sysTenantListMap = sysTenantService.queryListByTenantIds(tenantIds).stream()
                                                                  .collect(Collectors.toMap(e -> e.getTenantId(), a -> a));
        List<OrderItemShippingRecord> shippingRecords = new ArrayList<>();
        SysTenant sysTenant = new SysTenant();
//        log.info("准备进行订单第三方仓库后续操作,线程号:{}, orderList={}",Thread.currentThread().getId(), JSON.toJSONString(orderList));
        for (Orders order : orderList) {
            //先判断有无店铺标识
            sysTenant = sysTenantListMap.get(order.getTenantId());
            if (StringUtils.isEmpty(sysTenant.getThirdChannelFlag())) {
                //D4ZQGHV 为恒健分销商 定制业务，不需要推送erp
                String allowTenantId = sysConfigMapper.getSysConfigByKey("product.allow.examine");
                String[] split = allowTenantId.split("[，,;]");
                List<String> list = Arrays.asList(split);
                if (!list.contains(sysTenant.getTenantId())) {
                    // 如果当前租户不在允许的租户列表中，则执行传入Boolean.TRUE的逻辑
                    log.error("该分销商未配置店铺标识，无法推送,分销商： {},订单号：{}", order.getTenantId(), order.getOrderNo());

                }
                continue;
            }
            Long orderId = order.getId();
            String orderNo = order.getOrderNo();
            OrderStateType orderState = order.getOrderState();
            ChannelTypeEnum channelType = order.getChannelType();

            // 未支付成功的直接跳过
            if (!OrderStateType.Paid.equals(orderState)) {
                continue;
            }

            log.info("主订单ID{}, 订单{}, 状态{}, 开始第三方仓库后续操作", orderId, orderNo, orderState);
            List<OrderItem> bizArkOrderItemList = iOrderItemService.getListByOrderIdAndStockManager(orderId, StockManagerEnum.BizArk);

            if (CollUtil.isNotEmpty(bizArkOrderItemList)) {
                for (OrderItem orderItem : bizArkOrderItemList) {
                    String orderItemNo = orderItem.getOrderItemNo();
                    String shippingNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.ShippingRecordNo);

                    String tenantId = orderItem.getTenantId();
                    Long orderItemId = orderItem.getId();
                    String supplierTenantId = orderItem.getSupplierTenantId();
                    StockManagerEnum stockManager = orderItem.getStockManager();


                    OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItemId);
                    String warehouseCode = null;
                    String warehouseSystemCode = orderItemProductSku.getWarehouseSystemCode();
                    if (StrUtil.isNotBlank(warehouseSystemCode)) {
                        Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCodeNotTenant(warehouseSystemCode);
                        warehouseCode = warehouse.getWarehouseCode();
                    }

                    OrderItemShippingRecord shippingRecord = new OrderItemShippingRecord();
                    shippingRecord.setTenantId(tenantId);
                    shippingRecord.setSupplierTenantId(supplierTenantId);
                    shippingRecord.setOrderNo(orderNo);
                    shippingRecord.setOrderItemNo(orderItemNo);
                    shippingRecord.setChannelType(channelType);
                    shippingRecord.setWarehouseType(WarehouseTypeEnum.valueOf(stockManager.name()));
                    shippingRecord.setWarehouseCode(warehouseCode);
                    shippingRecord.setWarehouseSystemCode(warehouseSystemCode);
                    shippingRecord.setShippingNo(shippingNo);
                    shippingRecord.setShippingState(ShippingStateEnum.Creating);
                    shippingRecord.setSystemManaged(true);
                    shippingRecords.add(shippingRecord);
                }

            }
        }
        TenantHelper.ignore(()->iOrderItemShippingRecordService.saveBatch(shippingRecords));
        for (OrderItemShippingRecord shippingRecord : shippingRecords) {
            String orderNo = shippingRecord.getOrderNo();
            String shippingNo = shippingRecord.getShippingNo();
            String orderItemNo = shippingRecord.getOrderItemNo();
            WarehouseTypeEnum warehouseType = shippingRecord.getWarehouseType();
            StockManagerEnum stockManager = StockManagerEnum.getEnum(warehouseType.name());
            try{
                if (isNeedCreate) {

                    log.info("订单第三方仓库后续操作,线程号{}, 主订单{}，开始创建第三方发货单",Thread.currentThread().getId(), orderNo);
                    ThirdWarehouseEvent.createOrder(stockManager, shippingNo);
                }
            }catch (Exception e){
                iOrdersService.update(new LambdaUpdateWrapper<Orders>().eq(Orders::getOrderNo, orderNo)
                                                                       .set(Orders::getFulfillmentProgress, LogisticsProgress.UnDispatched));
                iOrderItemService.updateShippingOrderState(orderItemNo, ShippingOrderStateEnum.CreateFailed);
                if(ObjectUtil.isNotEmpty(shippingRecord)){
                    shippingRecord.setShippingState(ShippingStateEnum.CreateFailed);
                    iOrderItemShippingRecordService.updateById(shippingRecord);
                }

                log.error("订单第三方仓库后续操作 主订单{}，子订单{}，创建第三方发货单失败 {}", orderNo, orderItemNo, e.getMessage(), e);
            }
        }
    }

    /**
     * 订单更新物流信息
     *
     * @param orderList
     */
    private void orderUpdateLogistics(List<Orders> orderList) {
//        log.info("准备进行订单更新物流信息 orderList.size = {}", CollUtil.size(orderList));
        for (Orders order : orderList) {
            ChannelTypeEnum channelType = order.getChannelType();
            OrderStateType orderState = order.getOrderState();
            Long orderId = order.getId();
            String orderNo = order.getOrderNo();
            LogisticsTypeEnum logisticsType = order.getLogisticsType();
            // bug
            log.info("主订单ID{}, 订单{}, 物流类型{}, 状态{}, 开始更新物流信息", orderId, orderNo, logisticsType, orderState);
            if (OrderStateType.Paid.equals(orderState)) {
                OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderId(orderId);
                if (LogisticsTypeEnum.PickUp.equals(logisticsType) && (ChannelTypeEnum.Others.equals(channelType) || ChannelTypeEnum.Wayfair.equals(channelType))) {
                    // 目前Wayfair和Others且是自提才需要用到
                    List<OrderItem> orderItemList = iOrderItemService.getListByOrderId(orderId);
                    for (OrderItem orderItem : orderItemList) {
                        thirdPartyLogisticsSupport.queryLogisticsByOrderItem(orderItem);
                    }
                }
            }
        }
    }

    /**
     * 生成子订单相关的三张表OrderItem，OrderItemProductSku，OrderItemPrice
     * 注意：本接口不会做任何save操作，请在外部自行save
     * OrderItemProductSku和OrderItemPrice的orderItemId字段，因为OrderItem没有save，所以没有id，固无法存入
     * 在方法外调用后，先保存OrderItem，拿到id后set进OrderItemProductSku和OrderItemPrice，再保存这两张表
     *
     * @param order
     * @param productSku
     * @param paramDTO
     * @param zipCode
     * @param logisticsCarrier
     * @return
     * @throws Exception
     */
    public OrderItemDTO generateOrderItem(String dTenantId, Orders order, ProductSku productSku, Integer totalQuantity,
                                          OrderPriceCalculateDTO paramDTO, String zipCode, String logisticsCarrier) throws Exception {
        String supplierTenantId = productSku.getTenantId();
        String productCode = productSku.getProductCode();
        LogisticsTypeEnum logisticsType = order.getLogisticsType();

        Long productSkuId = productSku.getId();
        String sku = productSku.getSku();
        String erpSku = productSku.getErpSku();
        String upc = productSku.getUpc();
        StockManagerEnum stockManager = productSku.getStockManager();
        // 指定的活动ID方式不用了
//        String activityCode = paramDTO.getActivityCode();
        String productSkuCode = productSku.getProductSkuCode();
        String specComposeName = productSku.getSpecComposeName();
        String specValName = productSku.getSpecValName();
        String warehouseSystemCode = paramDTO.getWarehouseSystemCode();
        if(StrUtil.isNotBlank(logisticsCarrier)){
            paramDTO.setLogisticsCarrierCode(logisticsCarrier);
            paramDTO.setLogisticsCompany(logisticsCarrier);
        }
        Long productId = productSku.getProductId();
        Product product = iProductService.queryByIdIncludeDelete(productId);
        String productName = product.getName();
        String description = product.getDescription();
        String supplierProductCode = product.getProductCode();

        ProductSkuAttachmentVo firstImage = iProductSkuAttachmentService.queryFirstImageByProductSkuId(productSkuId);
        Long ossId = firstImage.getOssId();
        String attachmentSavePath = firstImage.getAttachmentSavePath();
        String attachmentShowUrl = firstImage.getAttachmentShowUrl();

        String orderNo = order.getOrderNo();
        Long channelId = order.getChannelId();
        ChannelTypeEnum channelType = order.getChannelType();

        OrderItem orderItem = new OrderItem();
        OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
        String orderItemNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderItemNo);

        orderItem.setSupplierTenantId(supplierTenantId);
        orderItem.setOrderNo(orderNo);
        orderItem.setOrderItemNo(orderItemNo);
        orderItem.setChannelId(channelId);
        orderItem.setChannelType(channelType);
        orderItem.setStockManager(stockManager);
        orderItem.setTotalQuantity(totalQuantity);
        orderItem.setProductSkuCode(productSkuCode);
        orderItem.setLogisticsType(logisticsType);

        orderItem.setSiteId(order.getSiteId());
        orderItem.setCurrency(order.getCurrency());
        orderItem.setCurrencySymbol(order.getCurrencySymbol());
        orderItem.setCountryCode(order.getCountryCode());


        paramDTO.setOrderItem(orderItem);
        List<OrderItem> orderItems = Collections.singletonList(orderItem);
        HashMap<String,List<String> >stashMap = getStashList(orderItems);
        List<String> stashList = stashMap.get(orderItemNo);
        OrderFlowEnum orderFlowEnum = OrderFlowEnum.IMPORT_ORDER;
        if(ObjectUtil.isNotEmpty(order.getOrderSource())){
            Integer orderSource = order.getOrderSource();
            if(OrderSourceEnum.EXCEL_ORDER.getValue().equals(orderSource)){
                orderFlowEnum = OrderFlowEnum.IMPORT_ORDER;
            }
            if(OrderSourceEnum.MALL_ORDER.getValue().equals(orderSource)){
                orderFlowEnum = OrderFlowEnum.Mall_CREATE_ORDER;
            }
        }
        // 获取商品相关的活动信息
        DistributorProductActivity distributorProductActivity = orderActivitySupport.getDistributorActivityByStock(dTenantId, productSkuCode, order.getCountryCode(),orderActivitySupport.logisticsTypeConvert(order.getLogisticsType()));
        if (null != distributorProductActivity) {
            orderItem.setActivityCode(distributorProductActivity.getDistributorActivityCode());
            orderItem.setActivityType(distributorProductActivity.getActivityType());
            order.setActivityType(distributorProductActivity.getActivityType());
            order.setActivityCode(distributorProductActivity.getDistributorActivityCode());
            orderItemProductSku.setActivityCode(distributorProductActivity.getDistributorActivityCode());
            orderItemProductSku.setActivityType(distributorProductActivity.getActivityType());
            DistributorProductActivityStock activityWarehouseInfo = orderActivitySupport.getActivityWarehouseInfo(distributorProductActivity);
            if(null != activityWarehouseInfo && null != activityWarehouseInfo.getWarehouseSystemCode()){
                orderItemProductSku.setSpecifyWarehouse(activityWarehouseInfo.getWarehouseSystemCode());
                orderItemProductSku.setWarehouseSystemCode(activityWarehouseInfo.getWarehouseSystemCode());
            }
        }
        paramDTO.setActivityCode(orderItem.getActivityCode());
        // 此时还有很多业务逻辑未生成最好是从外部拿 这里需要增加传参 一件代发/自提
        LocaleMessage localeMessage = priceSupportV2.calculationOrderItemPrice(paramDTO,dTenantId,zipCode,stashList,order,orderFlowEnum,logisticsCarrier );
        String logisticsCarrierCode = paramDTO.getLogisticsCarrierCode();
        paramDTO.setLogisticsCarrierCode(logisticsCarrierCode);
//        LocaleMessage localeMessage = this.calculationOrderItemPrice(paramDTO);
        OrderItemPrice orderItemPrice = paramDTO.getOrderItemPrice();

        orderItemPrice.setSiteId(order.getSiteId());
        orderItemPrice.setCurrency(order.getCurrency());
        orderItemPrice.setCurrencySymbol(order.getCurrencySymbol());

        orderItemProductSku.setSupplierTenantId(supplierTenantId);
        orderItemProductSku.setOrderNo(orderNo);
        orderItemProductSku.setOrderItemNo(orderItemNo);
        orderItemProductSku.setSku(sku);
        orderItemProductSku.setUpc(upc);
        orderItemProductSku.setErpSku(erpSku);
        orderItemProductSku.setChannelId(channelId);
        orderItemProductSku.setChannelType(channelType);
        orderItemProductSku.setProductCode(productCode);
        orderItemProductSku.setProductSkuCode(productSkuCode);
        orderItemProductSku.setProductName(productName);
        orderItemProductSku.setDescription(description);
        orderItemProductSku.setImageOssId(ossId);
        orderItemProductSku.setImageSavePath(attachmentSavePath);
        orderItemProductSku.setImageShowUrl(attachmentShowUrl);
        orderItemProductSku.setSpecComposeName(specComposeName);
        orderItemProductSku.setSpecValName(specValName);


//        // 此处可能生成了新的仓库编号
//        if(ObjectUtil.isNotEmpty(warehouseSystemCode)){
//            orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);
//            orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);
//        }else {
//            orderItemProductSku.setSpecifyWarehouse(paramDTO.getWarehouseSystemCode());
//            orderItemProductSku.setWarehouseSystemCode(paramDTO.getWarehouseSystemCode());
//        }

        // 仓库赋值逻辑修改为优先取paramDTO.getWarehouseSystemCode()中的仓库信息
        if(ObjectUtil.isNotEmpty(paramDTO.getWarehouseSystemCode())){
            orderItemProductSku.setSpecifyWarehouse(paramDTO.getWarehouseSystemCode());
            orderItemProductSku.setWarehouseSystemCode(paramDTO.getWarehouseSystemCode());
        }else {
            orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);
            orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);
        }

//        if (StrUtil.isNotBlank(activityCode)) {
//            ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder()
//                                                                                                               .activityCode(activityCode)
//                                                                                                               .build());
//            if (activityItem != null) {
//                orderItemProductSku.setActivityType(activityItem.getActivityType());
//                orderItemProductSku.setActivityCode(activityCode);
//                orderItem.setActivityType(activityItem.getActivityType());
//                orderItem.setActivityCode(activityCode);
//            }
//        }
        return new OrderItemDTO(orderItem, orderItemPrice, orderItemProductSku, localeMessage);
    }

    /**
     * 计算子订单价格
     * 计算好后会直接修改入参的DTO中的orderItem，并且会set一个新的OrderItemPrice
     * 此方法不做任何save操作，所有实体类在外部再自行保存
     *
     * @param paramDTO
     * @return
     */
    public LocaleMessage calculationOrderItemPrice(OrderPriceCalculateDTO paramDTO) {
        LocaleMessage localeMessage = new LocaleMessage();
        String country = paramDTO.getCountry();
        LogisticsTypeEnum logisticsType = paramDTO.getLogisticsType();
        ChannelTypeEnum channelTypeEnum = paramDTO.getChannelTypeEnum();
        OrderItem orderItem = paramDTO.getOrderItem();
        String orderItemNo = orderItem.getOrderItemNo();
        String productSkuCode = orderItem.getProductSkuCode();
        Integer totalQuantity = orderItem.getTotalQuantity();
        String activityCode = paramDTO.getActivityCode();
        Long siteId = null;
        siteId = iSiteCountryCurrencyService.getSiteIdByCountryCode(country);


        SupportedLogisticsEnum supportedLogistics = iProductService.querySupportedLogistics(productSkuCode);

        // 当前订单需要自提，但商品仅支持代发，返回提示
        if (LogisticsTypeEnum.PickUp.equals(logisticsType) && SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogistics)) {
            localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_DROP_SHIPPING.args(productSkuCode));
        }

        // 当前订单需要代发，但商品仅支持自提，返回提示
        if (LogisticsTypeEnum.DropShipping.equals(logisticsType) && SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)) {
            localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
        }

        //如果已经配置了新的定价方式，则从productSkuPrice中取价格
        OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItemNo);
        if (orderItemPrice == null) {
            orderItemPrice = new OrderItemPrice();
        }

        orderItemPrice.setOrderItemNo(orderItemNo);
        orderItemPrice.setProductSkuCode(productSkuCode);
        orderItemPrice.setLogisticsType(logisticsType);
        orderItemPrice.setTotalQuantity(totalQuantity);

        if (StrUtil.isNotBlank(activityCode)) {  // 存在活动编号，价格需要特殊处理
//            ProductActivityPriceItem activityPriceItem = iProductActivityPriceItemService.getByActivityCodeNoTenant(activityCode);
//            log.info("计算子订单价格 productSkuCode = {} activityPriceItem = {}", productSkuCode, JSONUtil.toJsonStr(activityPriceItem));
//            BeanUtil.copyProperties(activityPriceItem, orderItemPrice, "id", "delFlag", "createBy", "createTime", "updateBy", "updateTime");
//
//            orderItemPrice.setOriginalUnitPrice(activityPriceItem.getActivityUnitPrice());
//            orderItemPrice.setOriginalOperationFee(activityPriceItem.getActivityOperationFee());
//            orderItemPrice.setOriginalFinalDeliveryFee(activityPriceItem.getActivityFinalDeliveryFee());
//            orderItemPrice.setOriginalDepositUnitPrice(activityPriceItem.getActivityDepositUnitPrice());
//            orderItemPrice.setOriginalBalanceUnitPrice(activityPriceItem.getActivityBalanceUnitPrice());
//            orderItemPrice.setOriginalPickUpPrice(activityPriceItem.getActivityPickUpPrice());
//            orderItemPrice.setOriginalDropShippingPrice(activityPriceItem.getActivityDropShippingPrice());

            // 应付单价（供货商）
            BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(orderItemPrice.getOriginalPickUpPrice());
            // 预付单价（供货商，活动定价单价）
            BigDecimal originalPrepaidUnitPrice = NumberUtil.toBigDecimal(orderItemPrice.getOriginalDepositUnitPrice());
            // 实付单价（供货商，活动尾款单价）
            BigDecimal originalActualUnitPrice = NumberUtil.toBigDecimal(orderItemPrice.getOriginalBalanceUnitPrice());
            // 应付单价（平台、分销商）
            BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(orderItemPrice.getPlatformPickUpPrice());
            // 预付单价（平台、分销商，平台定价单价）
            BigDecimal platformPrepaidUnitPrice = NumberUtil.toBigDecimal(orderItemPrice.getPlatformDepositUnitPrice());
            // 实付单价（平台、分销商，平台尾款单价）
            BigDecimal platformActualUnitPrice = NumberUtil.toBigDecimal(orderItemPrice.getPlatformBalanceUnitPrice());
            if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                originalPayableUnitPrice = NumberUtil.toBigDecimal(orderItemPrice.getOriginalDropShippingPrice());
                platformPayableUnitPrice = NumberUtil.toBigDecimal(orderItemPrice.getPlatformDropShippingPrice());

                // 代发时，应付单价需要加上尾程派送费
                originalActualUnitPrice = NumberUtil.add(originalActualUnitPrice, orderItemPrice.getOriginalFinalDeliveryFee());
                platformActualUnitPrice = NumberUtil.add(platformActualUnitPrice, orderItemPrice.getPlatformFinalDeliveryFee());
            }

            // 活动订单需要支付订金，所以应付金额和实付金额是不一样的（应付金额为包含定金与尾款的完整价格，实付金额为尾款价格）
            // 原始应付总金额（供货商）
            BigDecimal originalPayableTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
            // 原始已预付总金额（供货商）
            BigDecimal originalPrepaidTotalAmount = NumberUtil.mul(originalPrepaidUnitPrice, totalQuantity);
            // 原始实付总金额（供货商）
            BigDecimal originalActualTotalAmount = NumberUtil.mul(originalActualUnitPrice, totalQuantity);
            // 应付总金额（平台、分销商）
            BigDecimal platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
            // 已预付总金额（平台、分销商）
            BigDecimal platformPrepaidTotalAmount = NumberUtil.mul(platformPrepaidUnitPrice, totalQuantity);
            // 实付总金额（平台、分销商）
            BigDecimal platformActualTotalAmount = NumberUtil.mul(platformActualUnitPrice, totalQuantity);

            // 供货商应得收入就是原始应付总金额
            orderItem.setSupplierIncomeEarned(originalPayableTotalAmount);
            orderItem.setOriginalPayableUnitPrice(originalPayableUnitPrice);
            orderItem.setOriginalPayableTotalAmount(originalPayableTotalAmount);
            orderItem.setOriginalPrepaidUnitPrice(originalPrepaidUnitPrice);
            orderItem.setOriginalPrepaidTotalAmount(originalPrepaidTotalAmount);
            orderItem.setOriginalActualUnitPrice(originalActualUnitPrice);
            orderItem.setOriginalActualTotalAmount(originalActualTotalAmount);
            orderItem.setOriginalRefundExecutableAmount(originalActualTotalAmount);

//            orderItemPrice.setOriginalDepositUnitPrice(activityPriceItem.getActivityDepositUnitPrice());
            orderItemPrice.setOriginalBalanceUnitPrice(originalActualUnitPrice);
            if (ChannelTypeEnum.Erp.equals(orderItem.getChannelType())) {

            } else {
                orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);
            }
            orderItem.setPlatformPayableTotalAmount(platformPayableTotalAmount);
            orderItem.setPlatformPrepaidUnitPrice(platformPrepaidUnitPrice);
            orderItem.setPlatformPrepaidTotalAmount(platformPrepaidTotalAmount);
            orderItem.setPlatformActualUnitPrice(platformActualUnitPrice);
            orderItem.setPlatformActualTotalAmount(platformActualTotalAmount);
            orderItem.setPlatformRefundExecutableAmount(platformActualTotalAmount);

//            orderItemPrice.setPlatformDepositUnitPrice(activityPriceItem.getPlatformDepositUnitPrice());
            orderItemPrice.setPlatformBalanceUnitPrice(platformActualUnitPrice);
        } else {  // 不存在活动编号，走正常价格
            // 会员价 改造

            Long finalSiteId = siteId;
            ProductSkuPrice productSkuPrice = TenantHelper.ignore(() -> iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode,
                finalSiteId));
            ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getById(productSkuPrice.getProductSkuId()));

            Product product = TenantHelper.ignore(() -> iProductService.getById(productSku.getProductId()));
            log.info("计算子订单价格 productSkuCode = {} productSkuPrice = {}", productSkuCode, JSONUtil.toJsonStr(productSkuPrice));
            // 结算会以product_sku_price
            orderItemPrice = getOrderItemPriceByChannel(productSkuPrice, orderItem, orderItemPrice);
            // todo 地址信息看看能不能拿到

            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), orderItem.getTenantId(), productSku.getId(),siteId );
            BigDecimal originalPickUpPrice = null;
            BigDecimal originalDropShippingPrice = null;
            originalPickUpPrice = productSkuPrice.getOriginalPickUpPrice();
            originalDropShippingPrice = productSkuPrice.getOriginalDropShippingPrice();
            BigDecimal platformPickUpPrice = null;
            platformPickUpPrice = getPlatformPickUpPrice(orderItem, productSkuPrice);

            BigDecimal platformDropShippingPrice = null;
            platformDropShippingPrice = productSkuPrice.getPlatformDropShippingPrice();

            BigDecimal platformFinalDeliveryFee = null;
            platformFinalDeliveryFee = productSkuPrice.getPlatformFinalDeliveryFee();
            if (ObjectUtil.isNotEmpty(memberPrice)) {
                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice())) {
                    originalPickUpPrice = memberPrice.getOriginalPickUpPrice();
                    orderItemPrice.setOriginalPickUpPrice(originalPickUpPrice);
                    orderItemPrice.setOriginalOperationFee(memberPrice.getOriginalOperationFee());

                    orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalDropShippingPrice())) {
                    originalDropShippingPrice = memberPrice.getOriginalDropShippingPrice();
                    orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
                    orderItemPrice.setOriginalOperationFee(memberPrice.getOriginalOperationFee());
                    orderItemPrice.setOriginalFinalDeliveryFee(memberPrice.getOriginalFinalDeliveryFee());
                    orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformPickUpPrice())) {
                    platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
                    orderItemPrice.setPlatformPickUpPrice(platformPickUpPrice);
                    orderItemPrice.setPlatformOperationFee(memberPrice.getPlatformOperationFee());
                    orderItemPrice.setPlatformUnitPrice(memberPrice.getOriginalUnitPrice());
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformDropShippingPrice())) {
                    platformDropShippingPrice = memberPrice.getPlatformDropShippingPrice();
                    platformFinalDeliveryFee = memberPrice.getPlatformFinalDeliveryFee();
                    orderItemPrice.setPlatformOperationFee(memberPrice.getPlatformOperationFee());
                    orderItemPrice.setPlatformFinalDeliveryFee(platformFinalDeliveryFee);
                    orderItemPrice.setPlatformDropShippingPrice(platformDropShippingPrice);
                    orderItemPrice.setPlatformUnitPrice(memberPrice.getOriginalUnitPrice());
                }
            }

            // 平台自提价（平台+分销商，产品单价+操作费）

//            BigDecimal platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();


            BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
            BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
            // 当前商品需要代发，但尾程派送费为零或者未设置，提示仅支持自提
            if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                if (NumberUtil.isLess(platformFinalDeliveryFee, BigDecimal.ZERO)) {
                    // 提示错误后，价格还要继续计算，只能只用自提价，但是要控制住订单不能支付
                    localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
                } else {
                    originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                    platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
                }
            }

            // 普通订单不需要支付订金，所以应付金额和实付金额是一样的
            // 原始应付总金额
            BigDecimal originalPayableTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
            // 原始实付总金额
            BigDecimal originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
            // 普通订单不需要支付定价，所以应付金额和实付金额是一样的
            // 平台应付总金额
            BigDecimal platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
            // 平台应付总金额
            BigDecimal platformActualTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);

            // 供货商应得收入就是原始应付总金额 tag lty
            orderItem.setSupplierIncomeEarned(originalPayableTotalAmount);
            orderItem.setOriginalPayableUnitPrice(originalPayableUnitPrice);
            orderItem.setOriginalPayableTotalAmount(originalPayableTotalAmount);
            orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            orderItem.setOriginalActualUnitPrice(originalPayableUnitPrice);
            orderItem.setOriginalActualTotalAmount(originalActualTotalAmount);
            orderItem.setOriginalRefundExecutableAmount(originalActualTotalAmount);

            orderItemPrice.setOriginalDepositUnitPrice(BigDecimal.ZERO);
            orderItemPrice.setOriginalBalanceUnitPrice(originalPayableUnitPrice);

            orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);
            orderItem.setPlatformPayableTotalAmount(platformPayableTotalAmount);
            //平台实际支付单价（平台、分销商）
            orderItem.setPlatformActualUnitPrice(platformPayableUnitPrice);

            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            //平台实际支付总金额（平台、分销商）
            orderItem.setPlatformActualTotalAmount(platformActualTotalAmount);

            orderItem.setPlatformRefundExecutableAmount(platformActualTotalAmount);

            orderItemPrice.setPlatformDepositUnitPrice(BigDecimal.ZERO);
            orderItemPrice.setPlatformBalanceUnitPrice(platformPayableUnitPrice);
        }

        paramDTO.setOrderItemPrice(orderItemPrice);
        return localeMessage;
    }

    /**
     * 功能描述：计算第三订单项目价格  platformUnitPrice 接口数据需要调整
     *
     * @param paramDTO 参数 dto
     * @return {@link OrderPriceCalculateDTO }
     * <AUTHOR>
     * @date 2023/12/28
     */
    public OrderPriceCalculateDTO calculationOrderItemPriceForThird(OrderPriceCalculateDTO paramDTO) {
        LocaleMessage localeMessage = new LocaleMessage();
        Long siteId = paramDTO.getSiteId();
        LogisticsTypeEnum logisticsType = paramDTO.getLogisticsType();
        OrderItem orderItem = paramDTO.getOrderItem();

        String orderItemNo = orderItem.getOrderItemNo();
        String productSkuCode = orderItem.getProductSkuCode();
        Integer totalQuantity = orderItem.getTotalQuantity();

        SupportedLogisticsEnum supportedLogistics = iProductService.querySupportedLogistics(productSkuCode);

        // 当前订单需要自提，但商品仅支持代发，返回提示
        if (LogisticsTypeEnum.PickUp.equals(logisticsType) && SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogistics)) {
            localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_DROP_SHIPPING.args(productSkuCode));
        }

        // 当前订单需要代发，但商品仅支持自提，返回提示
        if (LogisticsTypeEnum.DropShipping.equals(logisticsType) && SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)) {
            localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
        }

        //如果已经配置了新的定价方式，则从productSkuPrice中取价格
        ProductSkuPrice price = productAboutManger.getProductPriceByProductSkuCodeAndCountryCode(productSkuCode,orderItem.getCountryCode());
        OrderItemPrice orderItemPrice = new OrderItemPrice();
        if (orderItemPrice == null) {
            orderItemPrice = new OrderItemPrice();
        }

        orderItemPrice.setOrderItemNo(orderItemNo);
        orderItemPrice.setProductSkuCode(productSkuCode);
        orderItemPrice.setLogisticsType(logisticsType);
        orderItemPrice.setTotalQuantity(totalQuantity);

        // 不存在活动编号，走正常价格
        // todo 要观察这个siteId是否都能给到,要在order处,把数据埋点做好

        ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode,siteId);

        log.info("计算子订单价格 productSkuCode = {} productSkuPrice = {}", productSkuCode, JSONUtil.toJsonStr(productSkuPrice));
        BeanUtil.copyProperties(productSkuPrice, orderItemPrice, "id", "delFlag", "createBy", "createTime", "updateBy", "updateTime");
        // erp 推过来的 只有报关价
//        BigDecimal originalPickUpPrice = productSkuPrice.getOriginalPickUpPrice();
//        BigDecimal originalDropShippingPrice = productSkuPrice.getOriginalDropShippingPrice();
//        BigDecimal platformPickUpPrice = BigDecimal.ZERO;

        BigDecimal originalPayableTotalAmount = new BigDecimal(0);
        BigDecimal originalActualTotalAmount = new BigDecimal(0);
        BigDecimal platformActualTotalAmount = new BigDecimal(0);
        BigDecimal platformPayableTotalAmount = new BigDecimal(0);
        BigDecimal originalPayableUnitPrice = new BigDecimal(0);
        BigDecimal platformPayableUnitPrice = new BigDecimal(0);
        if(ObjectUtil.isNotEmpty(productSkuCode)){
            ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getById(productSkuPrice.getProductSkuId()));
            Product product = TenantHelper.ignore(() -> iProductService.getById(productSku.getProductId()));

            String tenantId = orderItem.getTenantId();

            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), tenantId, productSku.getId(), siteId);

            BigDecimal originalPickUpPrice = null;
            BigDecimal originalDropShippingPrice = null;
            originalPickUpPrice = productSkuPrice.getOriginalPickUpPrice();
            originalDropShippingPrice = productSkuPrice.getOriginalDropShippingPrice();
            BigDecimal platformPickUpPrice = null;
            platformPickUpPrice = getPlatformPickUpPriceForThird(orderItem, productSkuPrice);

            BigDecimal platformDropShippingPrice = null;
            platformDropShippingPrice = productSkuPrice.getPlatformDropShippingPrice();

            BigDecimal platformFinalDeliveryFee = null;
            platformFinalDeliveryFee = productSkuPrice.getPlatformFinalDeliveryFee();
            if (ChannelTypeEnum.Erp.name().equals(orderItem.getChannelType().name())) {
                platformPickUpPrice = orderItem.getPlatformActualUnitPrice();
            } else {
                platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
            }

            // erp逻辑需要拿推送数据订单价格
            originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
            platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
            // 当前商品需要代发，但尾程派送费为零或者未设置，提示仅支持自提
            if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                if (NumberUtil.isLess(platformFinalDeliveryFee, BigDecimal.ZERO)) {
                    // 提示错误后，价格还要继续计算，只能只用自提价，但是要控制住订单不能支付
                    localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
                } else {
                    originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                    platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
                }
            }

            if (ObjectUtil.isNotEmpty(memberPrice)) {
                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice())) {
                    originalPickUpPrice = memberPrice.getOriginalPickUpPrice();
                    orderItemPrice.setOriginalPickUpPrice(originalPickUpPrice);
                    orderItemPrice.setOriginalOperationFee(memberPrice.getOriginalOperationFee());

                    orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalDropShippingPrice())) {
                    originalDropShippingPrice = memberPrice.getOriginalDropShippingPrice();
                    orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
                    orderItemPrice.setOriginalOperationFee(memberPrice.getOriginalOperationFee());
                    orderItemPrice.setOriginalFinalDeliveryFee(memberPrice.getOriginalFinalDeliveryFee());
                    orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformPickUpPrice())) {
                    platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
                    orderItemPrice.setPlatformPickUpPrice(platformPickUpPrice);
                    orderItemPrice.setPlatformOperationFee(memberPrice.getPlatformOperationFee());
                    orderItemPrice.setPlatformUnitPrice(memberPrice.getOriginalUnitPrice());
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformDropShippingPrice())) {
                    platformDropShippingPrice = memberPrice.getPlatformDropShippingPrice();
                    platformFinalDeliveryFee = memberPrice.getPlatformFinalDeliveryFee();
                    orderItemPrice.setPlatformOperationFee(memberPrice.getPlatformOperationFee());
                    orderItemPrice.setPlatformFinalDeliveryFee(platformFinalDeliveryFee);
                    orderItemPrice.setPlatformDropShippingPrice(platformDropShippingPrice);
                    orderItemPrice.setPlatformUnitPrice(memberPrice.getOriginalUnitPrice());
                }
                // 普通订单不需要支付订金，所以应付金额和实付金额是一样的
                // 原始应付总金额
                originalPayableTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
                // 原始实付总金额
                originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
                // 普通订单不需要支付定价，所以应付金额和实付金额是一样的
                // 平台应付总金额
                platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
                // 平台应付总金额
                platformActualTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
            }
        }
        // 供货商应得收入就是原始应付总金额
        orderItem.setSupplierIncomeEarned(originalPayableTotalAmount);
        orderItem.setOriginalPayableUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setOriginalActualUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalActualTotalAmount(originalActualTotalAmount);
        orderItem.setOriginalRefundExecutableAmount(originalActualTotalAmount);

        orderItemPrice.setOriginalDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setOriginalBalanceUnitPrice(originalPayableUnitPrice);
        // 分销平台额外逻辑 ------------------------------------------------
        orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);
        orderItem.setPlatformPayableTotalAmount(platformPayableTotalAmount);

        orderItem.setPlatformActualUnitPrice(platformPayableUnitPrice);

        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setPlatformActualTotalAmount(platformActualTotalAmount);
        orderItem.setPlatformRefundExecutableAmount(platformActualTotalAmount);
        // 拿平台的金额保存进去
        BigDecimal platformUnitPrice = getPlatformUnitPrice(orderItem, price, paramDTO);
        orderItemPrice.setPlatformUnitPrice(platformUnitPrice);
        orderItemPrice.setPlatformDropShippingPrice(platformUnitPrice);
        orderItemPrice.setPlatformDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setPlatformBalanceUnitPrice(platformPayableUnitPrice);
        orderItemPrice.setPlatformPickUpPrice(platformUnitPrice);

        paramDTO.setOrderItemPrice(orderItemPrice);
        return paramDTO;
    }

    private BigDecimal getPlatformPickUpPriceForThird(OrderItem orderItem, ProductSkuPrice productSkuPrice) {
        ChannelTypeEnum channelType = orderItem.getChannelType();
        BigDecimal platformPickUpPrice = BigDecimal.ZERO;
        // 实际应该再加上source
        if(ObjectUtil.isNotEmpty(channelType)){
            platformPickUpPrice = orderItem.getPlatformActualUnitPrice();
        } else {
            platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
        }
        return platformPickUpPrice;
    }


    /**
     * 功能描述：计算第三订单项目价格  platformUnitPrice 接口数据需要调整
     *
     * @param paramDTO 参数 dto
     * @return {@link OrderPriceCalculateDTO }
     * <AUTHOR>
     * @date 2023/12/28
     */
    public OrderPriceCalculateDTO calculationOrderItemPriceForTemu(OrderPriceCalculateDTO paramDTO) {
        LocaleMessage localeMessage = new LocaleMessage();
        // todo 参考上面的 已经修改过的代码
        String country = paramDTO.getCountry();
        LogisticsTypeEnum logisticsType = paramDTO.getLogisticsType();
        OrderItem orderItem = paramDTO.getOrderItem();
        String orderItemNo = orderItem.getOrderItemNo();
        String productSkuCode = orderItem.getProductSkuCode();
        Integer totalQuantity = orderItem.getTotalQuantity();
        SiteCountryCurrency siteByCountryCode = new SiteCountryCurrency();
        Long siteId = null;
        siteId = iSiteCountryCurrencyService.getSiteIdByCountryCode(country);
        SupportedLogisticsEnum supportedLogistics = iProductService.querySupportedLogistics(productSkuCode);

        // 当前订单需要自提，但商品仅支持代发，返回提示
        if (LogisticsTypeEnum.PickUp.equals(logisticsType) && SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogistics)) {
            localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_DROP_SHIPPING.args(productSkuCode));
        }

        // 当前订单需要代发，但商品仅支持自提，返回提示
        if (LogisticsTypeEnum.DropShipping.equals(logisticsType) && SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)) {
            localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
        }

        //如果已经配置了新的定价方式，则从productSkuPrice中取价格
        ProductSkuPrice price = productAboutManger.getProductPriceByProductSkuCodeAndCountryCode(orderItem.getProductSkuCode(),orderItem.getCountryCode());
        OrderItemPrice orderItemPrice = new OrderItemPrice();

        orderItemPrice.setOrderItemNo(orderItemNo);
        orderItemPrice.setProductSkuCode(productSkuCode);
        orderItemPrice.setLogisticsType(logisticsType);
        orderItemPrice.setTotalQuantity(totalQuantity);

        // 不存在活动编号，走正常价格
        ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSkuCode,orderItem.getCountryCode());


        // erp 推过来的 只有报关价
//        BigDecimal originalPickUpPrice = productSkuPrice.getOriginalPickUpPrice();
//        BigDecimal originalDropShippingPrice = productSkuPrice.getOriginalDropShippingPrice();
//        BigDecimal platformPickUpPrice = BigDecimal.ZERO;
        BigDecimal originalPickUpPrice  = BigDecimal.ZERO;
        BigDecimal originalDropShippingPrice = BigDecimal.ZERO;
        BigDecimal platformPickUpPrice = BigDecimal.ZERO;
        ProductSku productSku;
        BigDecimal platformDropShippingPrice = BigDecimal.ZERO;
        BigDecimal platformFinalDeliveryFee = BigDecimal.ZERO;
        if (ObjectUtil.isNotNull(productSkuPrice)){
            log.info("计算子订单价格 productSkuCode = {} productSkuPrice = {}", productSkuCode, JSONUtil.toJsonStr(productSkuPrice));
            BeanUtil.copyProperties(productSkuPrice, orderItemPrice, "id", "delFlag", "createBy", "createTime", "updateBy", "updateTime");
             platformDropShippingPrice = productSkuPrice.getPlatformDropShippingPrice();
             platformFinalDeliveryFee = productSkuPrice.getPlatformFinalDeliveryFee();
            productSku = TenantHelper.ignore(() -> iProductSkuService.getById(productSkuPrice.getProductSkuId()));
        } else {
            orderItemPrice.setProductSkuCode("test");
            productSku = null;
        }
        if(ObjectUtil.isNotNull(productSku)){
            Product product = TenantHelper.ignore(() -> iProductService.getById(productSku.getProductId()));
            String tenantId = orderItem.getTenantId();


            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), tenantId, productSku.getId(), siteId);
            if (ObjectUtil.isNotEmpty(memberPrice)) {
                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice())) {
                    originalPickUpPrice = memberPrice.getOriginalPickUpPrice();
                    orderItemPrice.setOriginalPickUpPrice(originalPickUpPrice);
                    orderItemPrice.setOriginalOperationFee(memberPrice.getOriginalOperationFee());

                    orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalDropShippingPrice())) {
                    originalDropShippingPrice = memberPrice.getOriginalDropShippingPrice();
                    orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
                    orderItemPrice.setOriginalOperationFee(memberPrice.getOriginalOperationFee());
                    orderItemPrice.setOriginalFinalDeliveryFee(memberPrice.getOriginalFinalDeliveryFee());
                    orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformPickUpPrice())) {
                    platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
                    orderItemPrice.setPlatformPickUpPrice(platformPickUpPrice);
                    orderItemPrice.setPlatformOperationFee(memberPrice.getPlatformOperationFee());
                    orderItemPrice.setPlatformUnitPrice(memberPrice.getOriginalUnitPrice());
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformDropShippingPrice())) {
                    platformDropShippingPrice = memberPrice.getPlatformDropShippingPrice();
                    platformFinalDeliveryFee = memberPrice.getPlatformFinalDeliveryFee();
                    orderItemPrice.setPlatformOperationFee(memberPrice.getPlatformOperationFee());
                    orderItemPrice.setPlatformFinalDeliveryFee(platformFinalDeliveryFee);
                    orderItemPrice.setPlatformDropShippingPrice(platformDropShippingPrice);
                    orderItemPrice.setPlatformUnitPrice(memberPrice.getOriginalUnitPrice());
                }
            }
        }

        if (ObjectUtil.isNotNull(productSkuPrice)){
            originalPickUpPrice = productSkuPrice.getOriginalPickUpPrice();
            originalDropShippingPrice = productSkuPrice.getOriginalDropShippingPrice();

        }

        if (ChannelTypeEnum.Erp.name().equals(orderItem.getChannelType().name())) {
            platformPickUpPrice = orderItem.getPlatformActualUnitPrice();
        } else if (ChannelTypeEnum.Temu.name().equals(orderItem.getChannelType().name())){
            platformPickUpPrice = orderItem.getPlatformActualUnitPrice();
        }else {
            platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
        }

        // erp逻辑需要拿推送数据订单价格
        BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
        BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
        // 当前商品需要代发，但尾程派送费为零或者未设置，提示仅支持自提
        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
            if (NumberUtil.isLess(platformFinalDeliveryFee, BigDecimal.ZERO)) {
                // 提示错误后，价格还要继续计算，只能只用自提价，但是要控制住订单不能支付
                localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
            } else {
                originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
            }
        }
        // 普通订单不需要支付订金，所以应付金额和实付金额是一样的
        // 原始应付总金额
        BigDecimal originalPayableTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
        // 原始实付总金额
        BigDecimal originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
        // 普通订单不需要支付定价，所以应付金额和实付金额是一样的
        // 平台应付总金额
        BigDecimal platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
        // 平台应付总金额
        BigDecimal platformActualTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);

        // 供货商应得收入就是原始应付总金额
        orderItem.setSupplierIncomeEarned(originalPayableTotalAmount);
        orderItem.setOriginalPayableUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setOriginalActualUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalActualTotalAmount(originalActualTotalAmount);
        orderItem.setOriginalRefundExecutableAmount(originalActualTotalAmount);

        orderItemPrice.setOriginalDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setOriginalBalanceUnitPrice(originalPayableUnitPrice);
        // 分销平台额外逻辑 ------------------------------------------------
        orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);
        orderItem.setPlatformPayableTotalAmount(platformPayableTotalAmount);

        orderItem.setPlatformActualUnitPrice(platformPayableUnitPrice);

        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setPlatformActualTotalAmount(platformActualTotalAmount);
        orderItem.setPlatformRefundExecutableAmount(platformActualTotalAmount);
        // 拿平台的金额保存进去
        BigDecimal platformUnitPrice = getPlatformUnitPrice(orderItem, price, paramDTO);
        orderItemPrice.setPlatformUnitPrice(platformUnitPrice);
        orderItemPrice.setPlatformDropShippingPrice(platformUnitPrice);
        orderItemPrice.setPlatformDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setPlatformBalanceUnitPrice(platformPayableUnitPrice);
        orderItemPrice.setPlatformPickUpPrice(platformUnitPrice);

        paramDTO.setOrderItemPrice(orderItemPrice);
        return paramDTO;
    }

    /**
     * 功能描述：按渠道获取订单商品价格
     *
     * @param productSkuPrice 产品SKU价格
     * @param orderItem       订单项
     * @param orderItemPrice
     * @return {@link OrderItemPrice }
     * <AUTHOR>
     * @date 2024/02/08
     */
    private OrderItemPrice getOrderItemPriceByChannel(ProductSkuPrice productSkuPrice, OrderItem orderItem,
                                                      OrderItemPrice orderItemPrice) {
        if (ChannelTypeEnum.Erp.equals(orderItem.getChannelType())) {
            BigDecimal upPrice = orderItemPrice.getPlatformPickUpPrice();
            BeanUtil.copyProperties(productSkuPrice, orderItemPrice, "id", "delFlag", "createBy", "createTime", "updateBy", "updateTime,platformPickUpPrice", "platformUnitPrice", "platformDropShippingPrice");
            orderItemPrice.setPlatformPickUpPrice(upPrice);
        } else {
            BeanUtil.copyProperties(productSkuPrice, orderItemPrice, "id", "delFlag", "createBy", "createTime", "updateBy", "updateTime");
        }
        return orderItemPrice;
    }

    /**
     * 功能描述：获取平台取货价格
     *
     * @param orderItem       订单项
     * @param productSkuPrice 产品SKU价格
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/02/07
     */
    private BigDecimal getPlatformPickUpPrice(OrderItem orderItem, ProductSkuPrice productSkuPrice) {

        BigDecimal platformPickUpPrice = BigDecimal.ZERO;
        platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
        return platformPickUpPrice;
    }


    /**
     * 功能描述：获取平台单价
     *
     * @param orderItem 订单项
     * @param price     价格
     * @param paramDTO  参数 DTO
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/02/23
     */
    private BigDecimal getPlatformUnitPrice(OrderItem orderItem, ProductSkuPrice price,
                                            OrderPriceCalculateDTO paramDTO) {
        BigDecimal platformUnitPrice = null;
        if (ChannelTypeEnum.Erp.equals(orderItem.getChannelType())) {
            platformUnitPrice = orderItem.getPlatformActualUnitPrice();
        } else if (ChannelTypeEnum.TikTok.equals(orderItem.getChannelType())) {
            platformUnitPrice = orderItem.getPlatformActualUnitPrice();
        } else if (ChannelTypeEnum.Temu.equals(orderItem.getChannelType())) {
            platformUnitPrice = orderItem.getPlatformActualUnitPrice();
        } else {
            platformUnitPrice = price.getPlatformUnitPrice();
        }
        return platformUnitPrice;
    }


    /**
     * 地址信息转响应Body
     *
     * @param address
     * @return
     */
    public IntactAddressInfoVo addressToBody(OrderAddressInfo address) {
        IntactAddressInfoVo addressInfoBody = new IntactAddressInfoVo();
        String headerLanguage;
        try {
            headerLanguage = ServletUtils.getHeaderLanguage();
        } catch (Exception e) {
            headerLanguage = LanguageType.zh_CN.name();
        }

        if (ObjectUtil.isNotNull(address)) {
            String phoneNumber = address.getPhoneNumber();
            String recipient = address.getRecipient();
            addressInfoBody.setPhoneNumber(phoneNumber);
            addressInfoBody.setName(recipient);

            String country = address.getCountry();
            Long countryId = null;
            String countryCode = address.getCountryCode();
            if (ObjectUtil.isNotNull(countryCode)) {
                WorldLocation worldLocation = iWorldLocationService.queryByLocationCode(countryCode, LocationTypeEnum.Country);
                if (worldLocation != null) {
                    JSONObject locationOtherName = worldLocation.getLocationOtherName();
                    country = locationOtherName.get(headerLanguage, String.class);
                    countryId = worldLocation.getId();
                }
            }

            String state = address.getState();
            Long stateId = null;
            String stateCode = address.getStateCode();
            if (StrUtil.isNotBlank(stateCode)) {
                WorldLocation worldLocation = iWorldLocationService.queryByParentIdAndLocationCode(countryId, stateCode, LocationTypeEnum.State);
                if (worldLocation != null) {
                    JSONObject locationOtherName = worldLocation.getLocationOtherName();
                    state = locationOtherName.get(headerLanguage, String.class);
                    stateId = worldLocation.getId();
                }
            }

            addressInfoBody.setCountryId(countryId);
            addressInfoBody.setCountry(country);
            addressInfoBody.setCountryCode(countryCode);
            addressInfoBody.setStateId(stateId);
            addressInfoBody.setStateCode(stateCode);
            addressInfoBody.setState(state);
            addressInfoBody.setCity(address.getCity());
            addressInfoBody.setAddress1(address.getAddress1());
            addressInfoBody.setAddress2(address.getAddress2());
            addressInfoBody.setAddress3(address.getAddress3());
            addressInfoBody.setZip(address.getZipCode());
            addressInfoBody.setContactInformation(phoneNumber);
        }

        List<String> addressList = new LinkedList<>();
        addressList.add(StrUtil.isNotBlank(addressInfoBody.getAddress1()) ? addressInfoBody.getAddress1() : null);
        addressList.add(StrUtil.isNotBlank(addressInfoBody.getAddress2()) ? addressInfoBody.getAddress2() : null);
        addressList.add(StrUtil.isNotBlank(addressInfoBody.getAddress3()) ? addressInfoBody.getAddress3() : null);
        addressList.add(addressInfoBody.getCity());
        addressList.add(addressInfoBody.getState());
        addressList.add(addressInfoBody.getCountry());
        CollUtil.removeNull(addressList);
        String intactAddress = CollUtil.join(addressList, ", ");
        addressInfoBody.setIntactAddress(intactAddress);

        return addressInfoBody;
    }

    /**
     * 判断物流国家是否支持
     *
     * @param shippingCountry 收货国家
     * @param productSkuCode  ItemNo
     * @return
     */
    private void shippingCountrySupport(String activityCode, String shippingCountry, String productSkuCode,
                                        String specifyWarehouse) throws Exception {
        log.info("shippingCountrySupport - shippingCountry = {}, productSkuCode = {}", shippingCountry, productSkuCode);
        List<String> warehouseCountry = new ArrayList<>();
        if (StrUtil.isNotBlank(activityCode)) {
//            warehouseCountry = iProductActivityStockItemService.queryCountryByActivityCode(activityCode);
        } else {
            warehouseCountry = iProductSkuService.queryStockManagerCountry(productSkuCode, specifyWarehouse);
        }

        boolean allow = false;
        if (CollUtil.isNotEmpty(warehouseCountry)) {
            if (StrUtil.equalsAny(shippingCountry, "US", "CN") && warehouseCountry.contains(shippingCountry)) {
                allow = true;
            } else {
                // 支持的国家
                String parameterSupport = businessParameterService.getValueFromString(BusinessParameterType.SUPPORT_COUNTRY);
                JSONArray SUPPORT_COUNTRY = JSONUtil.parseArray(parameterSupport);
                // 剔除US和CN，就只剩下可以跨国发货的国家
                SUPPORT_COUNTRY.remove("US");
                SUPPORT_COUNTRY.remove("CN");
                if (SUPPORT_COUNTRY.contains(shippingCountry) && CollUtil.containsAny(SUPPORT_COUNTRY, warehouseCountry)) {
                    allow = true;
                }
            }
        }

        if (!allow) {
            throw new OrderPayException(OrderStatusCodeEnum.NOT_SUPPORTED_SHIP_TO.args(shippingCountry));
        }
    }

    /**
     * 重新计算订单金额 tag
     *
     * @param order
     * @param orderItemPriceList
     */
    public void recalculateOrderAmount(Orders order, List<OrderItemPrice> orderItemPriceList) {
        log.info("准备开始重新计算订单金额 子订单价格数据 = {}", JSONUtil.toJsonStr(orderItemPriceList));
        LogisticsTypeEnum logisticsType = order.getLogisticsType();
        BigDecimal originalTotalProductAmount = BigDecimal.ZERO;
        BigDecimal originalTotalOperationFee = BigDecimal.ZERO;
        BigDecimal originalTotalFinalDeliveryFee = BigDecimal.ZERO;
        BigDecimal originalTotalPickUpPrice = BigDecimal.ZERO;
        BigDecimal originalTotalDropShippingPrice = BigDecimal.ZERO;
        BigDecimal originalPayableTotalAmount = BigDecimal.ZERO;
        BigDecimal originalPrepaidTotalAmount = BigDecimal.ZERO;
        BigDecimal originalActualTotalAmount = BigDecimal.ZERO;
        BigDecimal originalRefundExecutableAmount = BigDecimal.ZERO;

        BigDecimal platformTotalProductAmount = BigDecimal.ZERO;
        BigDecimal platformTotalOperationFee = BigDecimal.ZERO;
        BigDecimal platformTotalFinalDeliveryFee = BigDecimal.ZERO;
        BigDecimal platformTotalPickUpPrice = BigDecimal.ZERO;
        BigDecimal platformTotalDropShippingPrice = BigDecimal.ZERO;
        BigDecimal platformPayableTotalAmount = BigDecimal.ZERO;
        BigDecimal platformPrepaidTotalAmount = BigDecimal.ZERO;
        BigDecimal platformActualTotalAmount = BigDecimal.ZERO;
        BigDecimal platformRefundExecutableAmount = BigDecimal.ZERO;
        // todo 要做order的数据埋点
        String countryCode = order.getCountryCode();
        SiteCountryCurrency siteByCountryCode = iSiteCountryCurrencyService.getSiteByCountryCode(countryCode);
        Long siteId = siteByCountryCode.getId();
        for (OrderItemPrice orderItemPrice : orderItemPriceList) {
            Integer totalQuantity = orderItemPrice.getTotalQuantity();
            // 会员定价逻辑
            ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, orderItemPrice.getProductSkuCode())));
            Product product = TenantHelper.ignore(() -> iProductService.getById(productSku.getProductId()));
            String tenantId = null;
            if (ObjectUtil.isNotEmpty(order.getTenantId())) {
                tenantId = order.getTenantId();
            } else {
                tenantId = LoginHelper.getTenantId();
            }
            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), tenantId, productSku.getId(), siteId);
            BigDecimal originalPickUpPrice = null;
            BigDecimal originalDropShippingPrice = null;
            originalPickUpPrice = orderItemPrice.getOriginalPickUpPrice();

            originalDropShippingPrice = orderItemPrice.getOriginalDropShippingPrice();
            BigDecimal platformPickUpPrice = null;
            platformPickUpPrice = orderItemPrice.getPlatformPickUpPrice();

            BigDecimal platformDropShippingPrice = null;
            platformDropShippingPrice = orderItemPrice.getPlatformDropShippingPrice();

            BigDecimal platformFinalDeliveryFee = null;
            platformFinalDeliveryFee = orderItemPrice.getPlatformFinalDeliveryFee();
            BigDecimal originalUnitPrice = null;
            originalUnitPrice = orderItemPrice.getOriginalUnitPrice();
            BigDecimal originalOperationFee = null;
            originalOperationFee = orderItemPrice.getOriginalOperationFee();
            BigDecimal originalFinalDeliveryFee = null;
            originalFinalDeliveryFee = orderItemPrice.getOriginalFinalDeliveryFee();

            BigDecimal platformUnitPrice = null;
            platformUnitPrice = orderItemPrice.getPlatformUnitPrice();
//          会员计价
            BigDecimal platformOperationFee = null;
            platformOperationFee = orderItemPrice.getPlatformOperationFee();

            if (ObjectUtil.isNotEmpty(memberPrice)) {
                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice())) {
                    originalPickUpPrice = memberPrice.getOriginalPickUpPrice();
                    if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                        orderItemPrice.setOriginalBalanceUnitPrice(memberPrice.getOriginalPickUpPrice());
                        orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                    }
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalDropShippingPrice())) {
                    originalDropShippingPrice = memberPrice.getOriginalDropShippingPrice();
                    originalFinalDeliveryFee = memberPrice.getOriginalFinalDeliveryFee();
                    if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                        orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                        orderItemPrice.setOriginalBalanceUnitPrice(memberPrice.getOriginalDropShippingPrice());
                    }
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformPickUpPrice())) {
                    if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                        orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                        orderItemPrice.setPlatformBalanceUnitPrice(memberPrice.getOriginalPickUpPrice());
                    }
                    platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformDropShippingPrice())) {
                    if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                        orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
                        orderItemPrice.setPlatformBalanceUnitPrice(memberPrice.getPlatformDropShippingPrice());
                    }
                    // tag lty
                    platformDropShippingPrice = memberPrice.getPlatformDropShippingPrice();
                    platformFinalDeliveryFee = memberPrice.getPlatformFinalDeliveryFee();
                }
                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice()) || ObjectUtil.isNotEmpty(memberPrice.getOriginalDropShippingPrice())) {
                    originalUnitPrice = memberPrice.getOriginalUnitPrice();

                    originalOperationFee = memberPrice.getOriginalOperationFee();
                    platformOperationFee = memberPrice.getPlatformOperationFee();
                }
            }
//            会员定价逻辑  OriginalBalanceUnitPrice  OriginalBalanceUnitPrice

            BigDecimal originalDepositUnitPrice = orderItemPrice.getOriginalDepositUnitPrice();
            BigDecimal originalBalanceUnitPrice = orderItemPrice.getOriginalBalanceUnitPrice();

            BigDecimal platformDepositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
            BigDecimal platformBalanceUnitPrice = orderItemPrice.getPlatformBalanceUnitPrice();

            BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
            BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
            if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);

                // 代发时，应付单价需要加上尾程派送费
                // originalBalanceUnitPrice = NumberUtil.add(originalBalanceUnitPrice, orderItemPrice.getOriginalFinalDeliveryFee());
                // platformBalanceUnitPrice = NumberUtil.add(platformBalanceUnitPrice, orderItemPrice.getPlatformFinalDeliveryFee());
            }

            originalTotalProductAmount = originalTotalProductAmount.add(NumberUtil.mul(originalUnitPrice, totalQuantity));
            originalTotalOperationFee = originalTotalOperationFee.add(NumberUtil.mul(originalOperationFee, totalQuantity));
            originalTotalFinalDeliveryFee = originalTotalFinalDeliveryFee.add(NumberUtil.mul(originalFinalDeliveryFee, totalQuantity));
            originalTotalPickUpPrice = originalTotalPickUpPrice.add(NumberUtil.mul(originalPickUpPrice, totalQuantity));
            originalTotalDropShippingPrice = originalTotalDropShippingPrice.add(NumberUtil.mul(originalDropShippingPrice, totalQuantity));

            originalPayableTotalAmount = originalPayableTotalAmount.add(NumberUtil.mul(originalPayableUnitPrice, totalQuantity));
            originalPrepaidTotalAmount = originalPrepaidTotalAmount.add(NumberUtil.mul(originalDepositUnitPrice, totalQuantity));
            originalActualTotalAmount = originalActualTotalAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));
            originalRefundExecutableAmount = originalRefundExecutableAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));

            platformTotalProductAmount = platformTotalProductAmount.add(NumberUtil.mul(platformUnitPrice, totalQuantity));
            platformTotalOperationFee = platformTotalOperationFee.add(NumberUtil.mul(platformOperationFee, totalQuantity));
            platformTotalFinalDeliveryFee = platformTotalFinalDeliveryFee.add(NumberUtil.mul(platformFinalDeliveryFee, totalQuantity));
            platformTotalPickUpPrice = platformTotalPickUpPrice.add(NumberUtil.mul(platformPickUpPrice, totalQuantity));
            platformTotalDropShippingPrice = platformTotalDropShippingPrice.add(NumberUtil.mul(platformDropShippingPrice, totalQuantity));

            platformPayableTotalAmount = platformPayableTotalAmount.add(NumberUtil.mul(platformPayableUnitPrice, totalQuantity));
            platformPrepaidTotalAmount = platformPrepaidTotalAmount.add(NumberUtil.mul(platformDepositUnitPrice, totalQuantity));
            // 这里改成主表供应商计算逻辑
            platformActualTotalAmount = platformActualTotalAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
//            platformActualTotalAmount = originalActualTotalAmount;
            platformRefundExecutableAmount = platformRefundExecutableAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
        }

        order.setOriginalTotalProductAmount(originalTotalProductAmount);
        order.setOriginalTotalOperationFee(originalTotalOperationFee);
        order.setOriginalTotalFinalDeliveryFee(originalTotalFinalDeliveryFee);
        order.setOriginalTotalPickUpPrice(originalTotalPickUpPrice);
        order.setOriginalTotalDropShippingPrice(originalTotalDropShippingPrice);
        order.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        order.setOriginalPrepaidTotalAmount(originalPrepaidTotalAmount);
        order.setOriginalActualTotalAmount(originalActualTotalAmount);
        order.setOriginalRefundExecutableAmount(originalRefundExecutableAmount);

        order.setPlatformTotalProductAmount(platformTotalProductAmount);
        order.setPlatformTotalOperationFee(platformTotalOperationFee);
        order.setPlatformTotalFinalDeliveryFee(platformTotalFinalDeliveryFee);

        order.setPlatformTotalPickUpPrice(platformTotalPickUpPrice);

        order.setPlatformTotalDropShippingPrice(platformTotalDropShippingPrice);
        order.setPlatformPayableTotalAmount(platformPayableTotalAmount);
        order.setPlatformPrepaidTotalAmount(platformPrepaidTotalAmount);

        order.setPlatformActualTotalAmount(platformActualTotalAmount);

        order.setPlatformRefundExecutableAmount(platformRefundExecutableAmount);
        log.info("重新计算后的订单金额 = {}", JSONUtil.toJsonStr(order));
    }

    /**
     * 归还库存（子订单）
     *
     * @param orderItemList
     */
    private void orderItemRestock(List<OrderItem> orderItemList) {
        for (OrderItem orderItem : orderItemList) {
            Long orderItemId = orderItem.getId();
            String orderItemNo = orderItem.getOrderItemNo();
            String productSkuCode = orderItem.getProductSkuCode();
            String activityCode = orderItem.getActivityCode();

            AdjustStockDTO dto = new AdjustStockDTO();
            OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItemId);

            dto.setAdjustQuantity(orderItem.getTotalQuantity());
            dto.setActivityCode(activityCode);
            dto.setSpecifyWarehouse(orderItemProductSku.getWarehouseSystemCode());
            dto.setProductSkuCode(productSkuCode);
            try {
                if (StrUtil.isNotBlank(activityCode)) {
                    ZSMallActivityEventUtils.activityStockAdjust(dto);
                } else {
                    productSkuStockService.adjustStock(dto, null);
                }
            } catch (Exception e) {
                log.info("子订单{}, 商品[{}]归还库存失败 {}", orderItemNo, productSkuCode, e.getMessage(), e);
            }
            //OpenAPI订单支付失败，仓库不重置
            Orders order = TenantHelper.ignore(() -> iOrdersService.getByOrderNo(orderItem.getOrderNo()));
            if (!(ObjectUtil.isNotNull(order)&&ObjectUtil.equal(orderItem.getLogisticsType().name(),LogisticsTypeEnum.PickUp.name())&&
                ObjectUtil.notEqual(order.getOrderSource(),OrderSourceEnum.INTERFACE_ORDER.getValue()))){
                orderItemProductSku.setWarehouseSystemCode(null);
            }
            iOrderItemProductSkuService.updateById(orderItemProductSku);
        }
    }

    /**
     * 归还库存
     *
     * @param dtoList
     */
    private void adjustStockDTORestock(List<AdjustStockDTO> dtoList) {
        // 已调整的库存集合不为空，需要归还已占用的
        if (CollUtil.isNotEmpty(dtoList)) {
            for (AdjustStockDTO dto : dtoList) {
                String activityCode = dto.getActivityCode();
                String productSkuCode = dto.getProductSkuCode();
                Integer adjustQuantity = dto.getAdjustQuantity();
                dto.setAdjustQuantity(Math.abs(adjustQuantity));

                try {
                    if (StrUtil.isNotBlank(activityCode)) {
                        ZSMallActivityEventUtils.activityStockAdjust(dto);
                    } else {
                        productSkuStockService.adjustStock(dto, null);
                    }
                } catch (Exception e) {
                    log.info("商品[{}]归还库存失败 {}", productSkuCode, e.getMessage(), e);
                }
            }
        }
    }


    /**
     * 批量更新主订单的物流状态
     *
     * @param orderIds
     */
    public void setOrderFulfillmentProgress(Set<Long> orderIds) {
        log.info("setOrderFulfillmentProgress orderIds = {}", orderIds);
        List<Orders> orders = iOrdersService.queryListByIds(orderIds);
        if (CollectionUtils.isNotEmpty(orders)) {
            for (Orders order : orders) {
                String orderNo = order.getOrderNo();
                // 处理复合状态
                Set<LogisticsProgress> fulfillmentTypes = iOrderItemService.queryFulfillmentTypesByOrderId(order.getId());
                log.info("setOrderFulfillment - orderNo = {}, fulfillmentTypes = {}", orderNo, fulfillmentTypes);
                LogisticsProgress originFulfillment = order.getFulfillmentProgress();
                order.setFulfillmentProgress(LogisticsProgress.getComplexType(fulfillmentTypes));

                // 现货订单若在此完成，需要计入账单（NotEqual是为了判断主订单是否是从其他状态变成Fulfilled的）TODO
                /*if (ObjectUtil.equals(order.getOrderType(), OrderType.Wholesale)
                    && ObjectUtil.equals(order.getFulfillmentProgress(), LogisticsProgress.Fulfilled)
                    && ObjectUtil.notEqual(order.getFulfillmentProgress(), originFulfillment)) {
                    wholesaleSupport.wholesaleOrderAddToBill(order);
                }*/
            }
        }
        iOrdersService.batchSaveOrUpdate(orders);
    }

    /**
     * 售出数量
     *
     * @param supProductCode
     * @param sku
     * @return
     */
    public Integer countSoldNumber(String orderState, String supProductCode, String sku) {
        return iOrderItemService.countSoldNumber(orderState, supProductCode, sku);
    }

    /**
     * 统计平台订单支付总金额（不包括批发）
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public BigDecimal statsPlatformOrderPaymentAmount(Date startDate, Date endDate) {
        return iOrderItemService.statsPlatformPayableTotalAmount(startDate, endDate);
    }

    /**
     * 统计平台订单支付总金额（批发用）
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public BigDecimal statsPlatformOrderPaymentAmount4Wholesale(Date startDate, Date endDate) {
        return iOrdersService.statsPlatformOrderPaymentAmount4Wholesale(startDate, endDate);
    }

    /**
     * 统计平台订单退款总金额
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public BigDecimal statsPlatformOrderRefundAmount(Date startDate, Date endDate) {
        return iOrderRefundService.sumPlatformRefundAmount(startDate, endDate);
    }

    /**
     * 批发订单支付
     *
     * @param wholesaleIntentionOrder
     * @param platformActualTotalAmount
     * @param transactionSubType
     * @throws Exception
     */
    public void wholesaleOrderPayChain(WholesaleIntentionOrder wholesaleIntentionOrder,
                                       BigDecimal platformActualTotalAmount,
                                       TransactionSubTypeEnum transactionSubType) {

        try {
            TransactionRecord transactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
            transactionRecord.setTransactionType(TransactionTypeEnum.Expenditure);
            transactionRecord.setTransactionSubType(transactionSubType);
            transactionRecord.setTransactionState(TransactionStateEnum.Processing);
            transactionRecord.setTransactionAmount(platformActualTotalAmount);
            tenantWalletService.walletChanges(transactionRecord);

            if (transactionSubType.equals(TransactionSubTypeEnum.WholesaleDeposit)) {
                Long wholesaleIntentionOrderId = wholesaleIntentionOrder.getId();
                TransactionsWholesaleIntentionOrder newRelation = new TransactionsWholesaleIntentionOrder();
                newRelation.setTransactionsId(transactionRecord.getId());
                newRelation.setWholesaleIntentionOrderId(wholesaleIntentionOrderId);
                iTransactionsWholesaleIntentionOrderService.save(newRelation);
            }
            if (transactionSubType.equals(TransactionSubTypeEnum.WholesaleBalance)) {
                // 创建交易记录和订单的关联关系
                Orders orders = iOrdersService.getByOrderNo(wholesaleIntentionOrder.getOrderNo());
                iTransactionsOrdersService.saveRelation(transactionRecord.getId(), orders.getId());
            }
        } catch (WalletException e) {
            log.info("批发订单{}钱包支付时出现异常（WalletException） {}", e.getMessage(), e);
            throw new RStatusCodeException(e.getStatusCode());
        } catch (Exception e) {
            log.info("批发订单{}钱包支付时出现未知异常 {}", e.getMessage(), e);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.WHOLESALE_PLACE_ORDER_ERROR);
        }


    }

    /**
     * 批发订金退至钱包
     *
     * @param wholesaleIntentionOrderId
     * @param orderDepositAmountPlatform
     */
    public void wholesaleRefundToWallet(Long wholesaleIntentionOrderId, BigDecimal orderDepositAmountPlatform) {

        try {
            TransactionRecord transactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
            transactionRecord.setTransactionAmount(orderDepositAmountPlatform);
            transactionRecord.setTransactionType(TransactionTypeEnum.Income);
            transactionRecord.setTransactionSubType(TransactionSubTypeEnum.WholesaleDepositRefund);
            transactionRecord.setTransactionState(TransactionStateEnum.Processing);
            tenantWalletService.walletChanges(transactionRecord);
            // 保存关联关系
            TransactionsWholesaleIntentionOrder newRelation = new TransactionsWholesaleIntentionOrder();
            newRelation.setTransactionsId(transactionRecord.getId());
            newRelation.setWholesaleIntentionOrderId(wholesaleIntentionOrderId);
            iTransactionsWholesaleIntentionOrderService.save(newRelation);
        } catch (WalletException e) {
            throw new RStatusCodeException(e.getStatusCode());
        } catch (Exception e) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.WALLET_REFUND_ERROR);
        }
    }

    /**
     * 检查订单是否有商品未映射异常
     *
     * @param orderList
     * @throws ProductException
     */
    public void orderStatusCheck(List<Orders> orderList) throws ProductException {
        if(CollUtil.isNotEmpty(orderList)){
            // amazonVC订单仓库判断
            checkOrdersWarehouse(orderList);
            Iterator<Orders> iterator = orderList.iterator();

            while (iterator.hasNext()) {

                Orders order = iterator.next();
                Integer exceptionCode = order.getExceptionCode();

                boolean isException = OrderExceptionEnum.product_mapping_exception.getValue().equals(exceptionCode)
                    && OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(exceptionCode);

                BigDecimal originalTotalDropShippingPrice = order.getOriginalTotalDropShippingPrice();
                BigDecimal originalTotalFinalDeliveryFee = order.getOriginalTotalFinalDeliveryFee();

                boolean deliveryFeeIsNull = ObjectUtil.isEmpty(originalTotalDropShippingPrice) && ObjectUtil.isEmpty(originalTotalFinalDeliveryFee);

                if (LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType()) && isException && deliveryFeeIsNull
                    ) {
                    order.setOrderState(OrderStateType.Failed);
                    order.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_STATE_EXCEPTION)
                                                          .toJSON());
                    iOrdersService.updateById(order);
                    iterator.remove();
                    continue;
                }
                // 如果是物流附件异常，移除订单
                if(OrderExceptionEnum.logistics_attachment_exception.getValue().equals(exceptionCode) || OrderExceptionEnum.warehouse_mapping_exception.getValue().equals(exceptionCode)){
                    order.setOrderState(OrderStateType.Failed);
                    order.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_STATE_EXCEPTION)
                                                          .toJSON());
                    iOrdersService.updateById(order);
                    iterator.remove();
                     continue;
                }
                // amazonVC和EC接口订单tracking信息判断
                if(ChannelTypeEnum.EC.equals(order.getChannelType()) || ChannelTypeEnum.Amazon_VC.equals(order.getChannelType())) {
                    if (LogisticsTypeEnum.PickUp.equals(order.getLogisticsType()) && 1 == order.getOrderSource()) {
                        List<OrderItemTrackingRecord> orderItemTrackingRecords = iOrderItemTrackingRecordService.getListByOrderNo(order.getOrderNo());
                        if (CollUtil.isEmpty(orderItemTrackingRecords)) {
                            order.setOrderState(OrderStateType.Failed);
                            order.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_NO_TRACKING_INFO)
                                                                  .toJSON());
                            iOrdersService.updateById(order);
                            iterator.remove();
                        } else {
                            for (OrderItemTrackingRecord orderItemTrackingRecord : orderItemTrackingRecords) {
                                String trackingNo = orderItemTrackingRecord.getLogisticsTrackingNo();
                                if (StringUtils.isEmpty(trackingNo)) {
                                    order.setOrderState(OrderStateType.Failed);
                                    order.setPayErrorMessage(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_NO_TRACKING_INFO)
                                                                          .toJSON());
                                    iOrdersService.updateById(order);
                                    iterator.remove();
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 补全商品映射失败的订单信息,根据渠道sku
     *
     * @param channelSkuList
     */
    @Transactional(rollbackFor = Exception.class)
    public void orderCompletionProductMappingException(List<String> channelSkuList) throws OrderPayException, StockException, ProductException {
        log.info("接收到映射模块通知的渠道SKU"+channelSkuList);
        if(CollUtil.isEmpty(channelSkuList)){
            return;
        }
        String tenantId = new String();
        List<String> orderNoList = new ArrayList<>();
        List<OrderLogisticsInfo> orderLogisticsInfos = new ArrayList<>();
        List<Orders> amazonVCOrderList = new ArrayList<>();
        try {
            // 判断是不是映射异常的数据
            Integer orderNum = iOrdersService.getByChannelSkuAndExceptionCodeNum(channelSkuList,OrderExceptionEnum.product_mapping_exception.getValue(),OrderStateType.UnPaid,OrderSourceEnum.INTERFACE_ORDER);
            if(orderNum == 0){
                List<Orders> exceptionOrderList;
                int offset = 0;
                int pageSize = 1000;
                do {
                    // 编辑商品映射的时候修改订单相关价格
                    exceptionOrderList = iOrdersService.getByChannelSkuAndExceptionCodePage(pageSize,offset,channelSkuList,null,OrderStateType.UnPaid,OrderSourceEnum.INTERFACE_ORDER);
                    log.info("待支付订单信息补全，订单信息:{} ",exceptionOrderList);
                    if(CollUtil.isNotEmpty(exceptionOrderList)) {
                        // OrderItemProductSku集合
                        List<OrderItemProductSku> orderItemProductSkus = new ArrayList<>();
                        // 订单明细集合
                        List<OrderItem> orderItemNewList = new ArrayList<>();
                        // 订单集合
                        List<Orders> updateOrderList = new ArrayList<>();
                        // 订单明细价格集合
                        List<OrderItemPrice> orderItemPriceTempList = new ArrayList<>();
                        // OrderItemPrice集合
                        List<OrderItemPrice> orderItemPriceList = new ArrayList<>();
                        // orderItemId集合
                        List<Long> orderItemIdList = new ArrayList<>();
                        // 映射修改的情况
                        HashMap<String, Integer> codesMap = new HashMap<String, Integer>();

                        for (Orders orders : exceptionOrderList) {
                            // amazonVC订单
                            if(ChannelTypeEnum.EC.equals(orders.getChannelType()) || ChannelTypeEnum.Amazon_VC.equals(orders.getChannelType())){
                                if(LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
                                    amazonVCOrderList.add(orders);
                                }
                            }
                            if (StringUtils.isEmpty(tenantId)) {
                                tenantId = orders.getTenantId();
                            }
                            // order_item_price、order_item_product_sku 表信息补充
                            List<OrderItem> orderItemList = iOrderItemService.getListByOrderId(orders.getId());
                            if (CollUtil.isEmpty(orderItemList)) {
                                log.info("待支付订单信息补全,订单:{} 没有item信息",orders);
                                continue;
                            }
                            String logisticsCompanyName = null;
                            OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orders.getOrderNo());
                            if(ObjectUtil.isNotEmpty(logisticsInfo)){
                                logisticsCompanyName = logisticsInfo.getLogisticsCompanyName();
                            }
                            HashMap<String,List<String> >stashMap = getStashList(orderItemList);
                            // 标识是否有产品映射
                            Boolean isMapped = true;
                            for (OrderItem orderItem : orderItemList) {
                                orderItemIdList.add(orderItem.getId());
                                List<ProductMapping> productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncState(orders.getTenantId(), orders.getChannelId(), orderItem.getChannelSku(), SyncStateEnum.Mapped);
                                // 拿地址信息
                                OrderAddressInfo addressInfo = iOrderAddressInfoService.getByOrderNo(orderItem.getOrderNo());
                                if (ObjectUtil.isEmpty(addressInfo)){
                                    throw new RuntimeException("订单地址信息不存在");
                                }
                                if (CollUtil.isEmpty(productMapping)) {
                                    log.info("待支付订单信息补全,订单:{} 没有产品映射信息",orders);
                                    isMapped = false;
                                    continue;
                                }
                                ProductMapping productMappingCountry = new ProductMapping();
                                if(null != addressInfo && StringUtils.isNotEmpty(addressInfo.getCountryCode())){
                                    productMappingCountry = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(orders.getTenantId(), orders.getChannelId(), orderItem.getChannelSku(), SyncStateEnum.Mapped,addressInfo.getCountryCode());
                                    if(ObjectUtil.isEmpty(productMappingCountry)){
                                        log.info("待支付订单信息补全,订单:{} 没有产品映射的站点信息",orders);
                                        LocaleMessage localeMessage = LocaleMessage.byStatusCode(OrderStatusCodeEnum.SKU_REGION_PRICE_NOT_MAINTAINED.args(productMapping.get(0).getProductSkuCode()));
                                        orders.setPayErrorMessage(localeMessage.toJSON());
                                        isMapped = false;
                                        updateOrderList.add(orders);
                                        continue;
                                    }
                                }
                                // 判断库存是否足够
                                ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productMappingCountry.getProductSkuCode());
                                Integer stockTotal = adjustStockVo.getStockTotal();
                                if (NumberUtil.compare(orderItem.getTotalQuantity(), stockTotal) > 0) {
                                    log.info("待支付订单信息补全,库存不足:{}",orders);
                                    if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                                        orders.setExceptionCode(OrderExceptionEnum.measurement_anomaly.getValue());
                                    }
                                    if(LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
                                        orders.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                                    }
                                }
                                // 判断库存够 那下面的stashList里是会有数据的
                                List<String> stashList = new ArrayList<>();
                                if(CollUtil.isNotEmpty(stashMap)){
                                    stashList = stashMap.get(orderItem.getOrderItemNo());
                                }
                                orderItem.setProductSkuCode(productMappingCountry.getProductSkuCode());
                                orderItem.setSupplierTenantId(productMappingCountry.getSupplierTenantId());
                                // 订单活动判断
                                // 获取商品相关的活动信息
                                DistributorProductActivity distributorProductActivity = orderActivitySupport.getDistributorActivityByStock(orderItem.getTenantId(), orderItem.getProductSkuCode(), orderItem.getCountryCode(),orderActivitySupport.logisticsTypeConvert(orders.getLogisticsType()));
                                // 判断活动订单
                                if (null != distributorProductActivity) {
                                    orderItem.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                                    orderItem.setActivityType(distributorProductActivity.getActivityType());
                                    orders.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                                    orders.setActivityType(distributorProductActivity.getActivityType());
                                }
                                OrderItemPrice orderItemPrice = new OrderItemPrice();
                                // 订单价格补充
                                OrderPriceCalculateDTO orderPriceCalculateDTO = new OrderPriceCalculateDTO();
                                orderPriceCalculateDTO.setOrderItem(orderItem);
                                orderPriceCalculateDTO.setLogisticsType(orders.getLogisticsType());
                                orderPriceCalculateDTO.setActivityCode(orderItem.getActivityCode());
                                LocaleMessage calculationOrderItemPriceExceptionMessage = priceSupportV2.calculationOrderItemPrice(orderPriceCalculateDTO, tenantId, addressInfo.getZipCode(),stashList, orders, OrderFlowEnum.MAPPING_CLEANING_ORDER, logisticsCompanyName );



                                if (calculationOrderItemPriceExceptionMessage.hasData()) {
                                    log.info("待支付订单信息补全,计算价格时出现异常:{},订单:{}",calculationOrderItemPriceExceptionMessage.toJSON(),orders);
                                    orders.setPayErrorMessage(calculationOrderItemPriceExceptionMessage.toJSON());
                                }
                                OrderItemPrice orderItemPrice1 = orderPriceCalculateDTO.getOrderItemPrice();
                                BeanUtils.copyProperties(orderItemPrice1, orderItemPrice);
                                OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
                                // 设置 orderItemProductSku 数据
                                setOrderItemProductSku(orderItemProductSku, orderItem, productMappingCountry);
                                orderItemProductSku.setOrderItemId(orderItem.getId());
                                orderItemProductSkus.add(orderItemProductSku);
                                // 设置 orderItemPrice
                                orderItemPrice.setTotalQuantity(orderItem.getTotalQuantity())
                                              .setOrderItemId(orderItem.getId()).setOrderItemNo(orderItem.getOrderItemNo())
                                              .setProductSkuCode(productMappingCountry.getProductSkuCode())
                                              .setLogisticsType(orderItem.getLogisticsType());
                                orderItemPrice.setId(null);
                                if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                                    logisticsInfo.setLogisticsCarrierCode(orderPriceCalculateDTO.getLogisticsCarrierCode());
                                    logisticsInfo.setLogisticsServiceName(orderPriceCalculateDTO.getLogisticsCode());
                                    logisticsInfo.setLogisticsCompanyName(orderPriceCalculateDTO.getLogisticsCarrierCode());
                                    orderLogisticsInfos.add(logisticsInfo);
                                    String warehouseSystemCode = orderPriceCalculateDTO.getWarehouseSystemCode();
                                    if(ObjectUtil.isNotEmpty(warehouseSystemCode)){
                                        orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);
                                    }else {
                                        orderItemProductSku.setWarehouseSystemCode(null);
                                    }
                                    orderItemProductSkus.add(orderItemProductSku);
                                }

                                orderItemPriceList.add(orderItemPrice);
                                orderItemPriceTempList.add(orderItemPrice);
                                OrderItem orderItemNew = new OrderItem();
                                // 复制订单明细信息
                                BeanUtils.copyProperties(orderItem, orderItemNew);
                                orderItemNewList.add(orderItemNew);
                            }
                            if(isMapped) {
                                log.info("待支付订单信息补全,重新计算主订单数据:{}", orders);
                                orderNoList.add(orders.getOrderNo());
                                // 重新计算主订单数据
                                priceSupportV2.recalculateOrderAmount(orders,orderItemPriceTempList);
                                if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                                    codesMap.put(orders.getOrderNo(),orders.getExceptionCode());
                                }

                            }
                        }
                        log.info("待支付订单信息补全,订单数据补全结束:{}",exceptionOrderList);
                        // 软删除订单明细价格、订单明细sku信息
                        if (CollUtil.isNotEmpty(orderItemIdList)) {
                            iOrderItemProductSkuService.updateDelFlagByOrderItemIdList(orderItemIdList, 2);
                            iOrderItemPriceService.updateDelFlagByOrderItemIdList(orderItemIdList, 2);
                        }
                        if (CollUtil.isNotEmpty(orderItemPriceList)) {
                            orderItemPriceService.saveOrSetNull(orderItemPriceList);
                        }
                        if (CollUtil.isNotEmpty(orderItemProductSkus)) {
                            iOrderItemProductSkuService.saveBatch(orderItemProductSkus);
                        }
                        if (CollUtil.isNotEmpty(orderItemNewList)) {
                            orderItemService.updateBatchOrSetNUll(orderItemNewList,codesMap);
                        }
                        if (CollUtil.isNotEmpty(updateOrderList)) {
                            iOrdersService.updateBatchOrSetNull(updateOrderList,codesMap);
                        }
                        if(CollUtil.isNotEmpty(orderLogisticsInfos)){
                            iOrderLogisticsInfoService.updateBatchOrSetNull(orderLogisticsInfos,codesMap);
                        }
                    }
                    offset += pageSize;
                }while (!exceptionOrderList.isEmpty());
            }else {
                List<Orders> exceptionOrderList;
                int offset = 0;
                int pageSize = 1000;
                do {
                    exceptionOrderList = iOrdersService.getByChannelSkuAndExceptionCodePage(pageSize,offset,channelSkuList,OrderExceptionEnum.product_mapping_exception.getValue(),OrderStateType.UnPaid,OrderSourceEnum.INTERFACE_ORDER);
                    log.info("映射异常订单信息补全，订单信息:{} ",exceptionOrderList);
                    // OrderItemProductSku集合
                    List<OrderItemProductSku> orderItemProductSkus = new ArrayList<>();
                    // OrderItemPrice集合
                    List<OrderItemPrice> orderItemPriceList = new ArrayList<>();
                    // 订单明细集合
                    List<OrderItem> orderItemNewList = new ArrayList<>();
                    // 订单集合
                    List<Orders> updateOrderList = new ArrayList<>();
                    HashMap<String, Integer> codesMap = new HashMap<String, Integer>();
                    for (Orders orders : exceptionOrderList){
                        // amazonVC订单
                        if(ChannelTypeEnum.EC.equals(orders.getChannelType()) || ChannelTypeEnum.Amazon_VC.equals(orders.getChannelType())){
                            if(LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())) {
                                amazonVCOrderList.add(orders);
                            }
                        }
                        if(StringUtils.isEmpty(tenantId)){
                            tenantId = orders.getTenantId();
                        }
                        String logisticsCompanyName = null;
                        OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orders.getOrderNo());
                        if(ObjectUtil.isNotEmpty(logisticsInfo)){
                            logisticsCompanyName = logisticsInfo.getLogisticsCompanyName();
                        }

                        // order_item_price、order_item_product_sku 表信息补充
                        List<OrderItem> orderItemList = iOrderItemService.getListByOrderId(orders.getId());


                        if(CollUtil.isEmpty(orderItemList)){
                            log.info("映射异常订单信息补全,订单:{} 没有item信息",orders);
                            continue;
                        }
                        List<OrderItemPrice> orderItemPriceTempList = new ArrayList<>();
                        // 标识是否有产品映射
                        Boolean isMapped = true;
                        for(OrderItem orderItem : orderItemList){
                            OrderAddressInfo addressInfo = iOrderAddressInfoService.getByOrderNo(orderItem.getOrderNo());
                            if (ObjectUtil.isEmpty(addressInfo)){
                                log.error("订单地址信息不存在");
                                continue;
                            }
                            List<ProductMapping> productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncState(orders.getTenantId(), orders.getChannelId(), orderItem.getChannelSku(), SyncStateEnum.Mapped);
                            if(CollUtil.isEmpty(productMapping)){
                                log.info("映射异常订单信息补全,订单:{} 没有产品映射信息",orders);
                                isMapped = false;
                                continue;
                            }
                            ProductMapping productMappingCountry = new ProductMapping();
                            if(null != addressInfo && StringUtils.isNotEmpty(addressInfo.getCountryCode())){
                                productMappingCountry = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(orders.getTenantId(), orders.getChannelId(), orderItem.getChannelSku(), SyncStateEnum.Mapped,addressInfo.getCountryCode());
                                if(ObjectUtil.isEmpty(productMappingCountry)){
                                    log.info("映射异常订单信息补全,订单:{} 没有产品映射的站点信息",orders);
                                    LocaleMessage localeMessage = LocaleMessage.byStatusCode(OrderStatusCodeEnum.SKU_REGION_PRICE_NOT_MAINTAINED.args(productMapping.get(0).getProductSkuCode()));
                                    orders.setPayErrorMessage(localeMessage.toJSON());
                                    isMapped = false;
                                    updateOrderList.add(orders);
                                    continue;
                                }
                            }
                            // 判断库存是否足够
                            ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productMappingCountry.getProductSkuCode());
                            Integer stockTotal = adjustStockVo.getStockTotal();
                            if (NumberUtil.compare(orderItem.getTotalQuantity(), stockTotal) > 0) {
                                log.info("映射异常订单信息补全,库存不足:{}",orders);
                                LocaleMessage localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productMapping.get(0).getProductSkuCode()));
                                orders.setPayErrorMessage(localeMessage.toJSON());
                                orders.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                                updateOrderList.add(orders);
                                isMapped = false;
                            }
                            orderItem.setProductSkuCode(productMappingCountry.getProductSkuCode());
                            orderItem.setSupplierTenantId(productMappingCountry.getSupplierTenantId());
                            // 订单活动判断
                            // 获取商品相关的活动信息
                            DistributorProductActivity distributorProductActivity = orderActivitySupport.getDistributorActivityByStock(orderItem.getTenantId(), orderItem.getProductSkuCode(), orderItem.getCountryCode(),orderActivitySupport.logisticsTypeConvert(orders.getLogisticsType()));
                            // 判断活动订单
                            if (null != distributorProductActivity) {
                                orderItem.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                                orderItem.setActivityType(distributorProductActivity.getActivityType());
                                orders.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                                orders.setActivityType(distributorProductActivity.getActivityType());
                            }
                            OrderItemPrice orderItemPrice = new OrderItemPrice();
                            // 订单价格补充
                            OrderPriceCalculateDTO orderPriceCalculateDTO = new OrderPriceCalculateDTO();
                            orderPriceCalculateDTO.setOrderItem(orderItem);
                            orderPriceCalculateDTO.setLogisticsType(orders.getLogisticsType());
                            orderPriceCalculateDTO.setActivityCode(orderItem.getActivityCode());
                            HashMap<String,List<String> >stashMap = getStashList(orderItemList);
                            List<String> stashList = stashMap.get(orderItem.getOrderItemNo());
                            LocaleMessage calculationOrderItemPriceExceptionMessage = priceSupportV2.calculationOrderItemPrice(orderPriceCalculateDTO, tenantId, addressInfo.getZipCode(),stashList, orders, OrderFlowEnum.MAPPING_CLEANING_ORDER, logisticsCompanyName );
                            if(ObjectUtil.isNotEmpty(calculationOrderItemPriceExceptionMessage) && StringUtils.isNotEmpty(calculationOrderItemPriceExceptionMessage.getEn_US())){
                                log.info("映射异常订单信息补全,计算价格时出现异常:{},订单:{}",calculationOrderItemPriceExceptionMessage.toJSON(),orders);
                                orders.setPayErrorMessage(calculationOrderItemPriceExceptionMessage.toJSON());
                                updateOrderList.add(orders);
                                isMapped = false;
                            }
                            OrderItemPrice orderItemPrice1 = orderPriceCalculateDTO.getOrderItemPrice();
                            BeanUtils.copyProperties(orderItemPrice1, orderItemPrice);
                            OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
                            // 设置 orderItemProductSku 数据
                            setOrderItemProductSku(orderItemProductSku, orderItem, productMappingCountry);
                            orderItemProductSku.setOrderItemId(orderItem.getId());

                            if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                                logisticsInfo.setLogisticsCarrierCode(orderPriceCalculateDTO.getLogisticsCarrierCode());
                                logisticsInfo.setLogisticsServiceName(orderPriceCalculateDTO.getLogisticsCode());
                                logisticsInfo.setLogisticsCompanyName(orderPriceCalculateDTO.getLogisticsCarrierCode());
                                orderLogisticsInfos.add(logisticsInfo);
                                String warehouseSystemCode = orderPriceCalculateDTO.getWarehouseSystemCode();
                                if(ObjectUtil.isNotEmpty(warehouseSystemCode)){
                                    orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);
                                }else {
                                    orderItemProductSku.setWarehouseSystemCode(null);
                                }
                            }
                            orderItemProductSkus.add(orderItemProductSku);
                            // 设置 orderItemPrice
                            orderItemPrice.setTotalQuantity(orderItem.getTotalQuantity()).setOrderItemId(orderItem.getId()).setOrderItemNo(orderItem.getOrderItemNo()).setProductSkuCode(productMappingCountry.getProductSkuCode()).setLogisticsType(orderItem.getLogisticsType());
                            orderItemPriceList.add(orderItemPrice);
                            orderItemPriceTempList.add(orderItemPrice);
                            OrderItem orderItemNew = new OrderItem();
                            // 复制订单明细信息
                            BeanUtils.copyProperties(orderItem, orderItemNew);
                            orderItemNewList.add(orderItemNew);
                            if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                                codesMap.put(orders.getOrderNo(),orders.getExceptionCode());
                            }
                        }
                        if(isMapped){
                            log.info("映射异常订单信息补全,重新计算主订单数据:{}",orders);
                            orderNoList.add(orders.getOrderNo());
                            // 重新计算主订单数据
                            priceSupportV2.recalculateOrderAmount(orders,orderItemPriceTempList);
                            orders.setExceptionCode(OrderExceptionEnum.normal.getValue());
                            updateOrderList.add(orders);
                        }
                    }
                    log.info("待支付订单信息补全,订单数据补全结束:{}",exceptionOrderList);
                    if (CollUtil.isNotEmpty(orderItemProductSkus)){
                        iOrderItemProductSkuService.saveBatch(orderItemProductSkus);
                    }
                    if(CollUtil.isNotEmpty(orderItemPriceList)){
                        orderItemPriceService.saveOrSetNull(orderItemPriceList);
                    }
                    if(CollUtil.isNotEmpty(orderItemNewList)){
                        orderItemService.updateBatchOrSetNUll(orderItemNewList,codesMap);
                    }
                    if(CollUtil.isNotEmpty(updateOrderList)){
                        iOrdersService.updateBatchOrSetNull(updateOrderList,codesMap);
                    }
                    if(CollUtil.isNotEmpty(orderLogisticsInfos)){
                        iOrderLogisticsInfoService.updateBatchOrSetNull(orderLogisticsInfos,codesMap);
                    }
                    offset += pageSize;
                }while (!exceptionOrderList.isEmpty());
            }
        }catch (Exception e){
            log.error("产品映射，订单信息补全异常:",e);
            throw new RuntimeException();
        }
        // 支付逻辑
        try {
            if(CollUtil.isNotEmpty(orderNoList)){
                // 去除amazonVC订单
                if(CollUtil.isNotEmpty(amazonVCOrderList)){
                    List<String> amazonVCOrderNoList = amazonVCOrderList.stream().map(Orders::getOrderNo).collect(Collectors.toSet()).stream().collect(Collectors.toList());
                    if(CollUtil.isNotEmpty(amazonVCOrderNoList)){
                        orderNoList.removeAll(amazonVCOrderNoList);
                    }
                }
                if(CollUtil.isNotEmpty(orderNoList)){
                    List<Orders> ordersList ;
                    int offsetOrder = 0;
                    int pageSizeOrder = 30;
                    do {
                        ordersList  = iOrdersService.queryByOrderNoListPage(pageSizeOrder,offsetOrder,orderNoList);
                        List<Orders> ordersNeedPayList = null;
                        if(CollUtil.isNotEmpty(ordersList)){
                            // 订单自动支付检查
                            for(Orders orders: ordersList){
                                if(ZSMallSystemEventUtils.checkWalletAutoPaymentEvent(orders.getTenantId(),orders.getCurrency())){
                                    ordersNeedPayList.add(orders);
                                }
                            }
                            if(CollUtil.isNotEmpty(ordersNeedPayList)) {
                                orderPayChain(tenantId, ordersNeedPayList, true, true);
                            }
                        }
                        offsetOrder += pageSizeOrder;
                    }while (!ordersList.isEmpty());
                }
            }
        }catch (Exception e){
            log.error("产品映射修改订单数据出现异常：{}",e.getMessage());
        }
        // 仓库映射判断
        checkOrdersWarehouse(amazonVCOrderList);
        // amazonVC订单获取面单
        sendAmazonVCGetLogisticsAttachmentMessage(amazonVCOrderList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void orderCompletion(List<String> orderNoParamList) {
        log.info("订单补全订单号：{}",orderNoParamList);
        String tenantId = new String();
        List<String> orderNoList = new ArrayList<>();
        List<OrderLogisticsInfo> orderLogisticsInfos = new ArrayList<>();
        List<Orders> amazonVCOrderList = new ArrayList<>();
        try {
            List<Orders> exceptionOrderList;
            int offset = 0;
            int pageSize = 1000;
            do {
                exceptionOrderList = iOrdersService.getByOrderNoListAndExceptionCodePage(pageSize,offset,orderNoParamList,null,OrderStateType.UnPaid,OrderSourceEnum.INTERFACE_ORDER);
                log.info("接口订单信息补全，订单信息:{} ",exceptionOrderList);
                // OrderItemProductSku集合
                List<OrderItemProductSku> orderItemProductSkus = new ArrayList<>();
                // OrderItemPrice集合
                List<OrderItemPrice> orderItemPriceList = new ArrayList<>();
                // 订单明细集合
                List<OrderItem> orderItemNewList = new ArrayList<>();
                // 订单集合
                List<Orders> updateOrderList = new ArrayList<>();
                // 地址集合
                List<OrderLogisticsInfo> logisticsInfos = new ArrayList<>();
                HashMap<String, Integer> codesMap = new HashMap<String, Integer>();
                for (Orders orders : exceptionOrderList){
                    // amazonVC订单
                    if(ChannelTypeEnum.EC.equals(orders.getChannelType()) || ChannelTypeEnum.Amazon_VC.equals(orders.getChannelType())){
                        if(LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())) {
                            amazonVCOrderList.add(orders);
                        }
                    }
                    if(StringUtils.isEmpty(tenantId)){
                        tenantId = orders.getTenantId();
                    }
                    // order_item_price、order_item_product_sku 表信息补充
                    List<OrderItem> orderItemList = iOrderItemService.getListByOrderId(orders.getId());
                    if(CollUtil.isEmpty(orderItemList)){
                        log.info("接口订单信息补全,订单:{} 没有item信息",orders);
                        continue;
                    }
                    String logisticsCompanyName = null;

                    List<OrderItemPrice> orderItemPriceTempList = new ArrayList<>();
                    HashMap<String,List<String> >stashMap = getStashList(orderItemList);
                    OrderAddressInfo orderAddressInfo = iOrderAddressInfoService.getByOrderNo(orders.getOrderNo());
                    if (ObjectUtil.isEmpty(orderAddressInfo)){
                        log.error("订单地址信息不存在");
                        continue;
                    }
                    // 标识是否有产品映射
                    Boolean isMapped = true;
                    for(OrderItem orderItem : orderItemList){
                        List<ProductMapping> productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncState(orders.getTenantId(), orders.getChannelId(), orderItem.getChannelSku(), SyncStateEnum.Mapped);
                        if(CollUtil.isEmpty(productMapping)){
                            log.info("接口订单信息补全,订单:{} 没有产品映射信息",orders);
                            isMapped = false;
                            continue;
                        }
                        ProductMapping productMappingCountry = new ProductMapping();
                        if(null != orderAddressInfo && StringUtils.isNotEmpty(orderAddressInfo.getCountryCode())){
                            productMappingCountry = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(orders.getTenantId(), orders.getChannelId(), orderItem.getChannelSku(), SyncStateEnum.Mapped,orderAddressInfo.getCountryCode());
                            if(ObjectUtil.isEmpty(productMappingCountry)){
                                log.info("接口订单信息补全,订单:{} 没有产品映射信息",orders);
                                LocaleMessage localeMessage = LocaleMessage.byStatusCode(OrderStatusCodeEnum.SKU_REGION_PRICE_NOT_MAINTAINED.args(productMapping.get(0).getProductSkuCode()));
                                orders.setPayErrorMessage(localeMessage.toJSON());
                                isMapped = false;
                                updateOrderList.add(orders);
                                continue;
                            }
                        }
                        // 判断库存是否足够
                        ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productMappingCountry.getProductSkuCode());
                        Integer stockTotal = adjustStockVo.getStockTotal();
                        if (NumberUtil.compare(orderItem.getTotalQuantity(), stockTotal) > 0) {
                            log.info("接口订单信息补全,库存不足:{}",orders);
                            isMapped = false;
                            orders.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                        }
                        orderItem.setProductSkuCode(productMappingCountry.getProductSkuCode());
                        orderItem.setSupplierTenantId(productMappingCountry.getSupplierTenantId());
                        OrderItemPrice orderItemPrice = new OrderItemPrice();
                        // 订单活动判断
                        // 获取商品相关的活动信息
                        DistributorProductActivity distributorProductActivity = orderActivitySupport.getDistributorActivityByStock(orderItem.getTenantId(), orderItem.getProductSkuCode(), orderItem.getCountryCode(),orderActivitySupport.logisticsTypeConvert(orders.getLogisticsType()));
                        // 判断活动订单
                        if (null != distributorProductActivity) {
                            orderItem.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                            orderItem.setActivityType(distributorProductActivity.getActivityType());
                            orders.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                            orders.setActivityType(distributorProductActivity.getActivityType());
                        }
                        // 订单价格补充
                        OrderPriceCalculateDTO orderPriceCalculateDTO = new OrderPriceCalculateDTO();
                        orderPriceCalculateDTO.setOrderItem(orderItem);
                        orderPriceCalculateDTO.setLogisticsType(orders.getLogisticsType());
                        orderPriceCalculateDTO.setActivityCode(orderItem.getActivityCode());
                        OrderAddressInfo addressInfo = iOrderAddressInfoService.getByOrderNo(orderItem.getOrderNo());
                        OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orders.getOrderNo());
                        if(ObjectUtil.isNotEmpty(logisticsInfo)){
                            logisticsCompanyName = logisticsInfo.getLogisticsCompanyName();
                        }
                        List<String> stashList = stashMap.get(orderItem.getOrderItemNo());
                        LocaleMessage calculationOrderItemPriceExceptionMessage = priceSupportV2.calculationOrderItemPrice(orderPriceCalculateDTO, tenantId, addressInfo.getZipCode(),stashList, orders, OrderFlowEnum.MAPPING_CLEANING_ORDER, logisticsCompanyName );

                        if(ObjectUtil.isNotEmpty(calculationOrderItemPriceExceptionMessage) && StringUtils.isNotEmpty(calculationOrderItemPriceExceptionMessage.getEn_US())){
                            log.info("接口订单信息补全,计算价格时出现异常:{},订单:{}",calculationOrderItemPriceExceptionMessage.toJSON(),orders);
                            isMapped = false;
                            orders.setPayErrorMessage(calculationOrderItemPriceExceptionMessage.toJSON());
                        }
                        OrderItemPrice orderItemPrice1 = orderPriceCalculateDTO.getOrderItemPrice();
                        BeanUtils.copyProperties(orderItemPrice1, orderItemPrice);
                        OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
                        // 设置 orderItemProductSku 数据
                        setOrderItemProductSku(orderItemProductSku, orderItem, productMappingCountry);
                        orderItemProductSku.setOrderItemId(orderItem.getId());
                        if (LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                            String warehouseSystemCode = orderPriceCalculateDTO.getWarehouseSystemCode();
                            String logisticsCarrierCode = orderPriceCalculateDTO.getLogisticsCarrierCode();
                            String logisticsCode = orderPriceCalculateDTO.getLogisticsCode();
                            logisticsInfo.setLogisticsCompanyName(logisticsCarrierCode);
                            logisticsInfo.setLogisticsCarrierCode(logisticsCarrierCode);
                            logisticsInfo.setLogisticsServiceName(logisticsCode);
                            logisticsInfos.add(logisticsInfo);
                            orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);
                        }

                        orderItemProductSkus.add(orderItemProductSku);
                        // 设置 orderItemPrice
                        orderItemPrice.setTotalQuantity(orderItem.getTotalQuantity()).setOrderItemId(orderItem.getId()).setOrderItemNo(orderItem.getOrderItemNo()).setProductSkuCode(productMappingCountry.getProductSkuCode()).setLogisticsType(orderItem.getLogisticsType());
                        orderItemPriceList.add(orderItemPrice);
                        orderItemPriceTempList.add(orderItemPrice);
                        OrderItem orderItemNew = new OrderItem();
                        // 复制订单明细信息
                        BeanUtils.copyProperties(orderItem, orderItemNew);
                        orderItemNewList.add(orderItemNew);
                        if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                            codesMap.put(orders.getOrderNo(),orders.getExceptionCode());
                        }
                    }
                    if(isMapped){
                        log.info("接口订单信息补全,重新计算主订单数据:{}",orders);
                        orderNoList.add(orders.getOrderNo());
                        // 重新计算主订单数据
                        priceSupportV2.recalculateOrderAmount(orders,orderItemPriceTempList);
                        orders.setExceptionCode(OrderExceptionEnum.normal.getValue());
                        updateOrderList.add(orders);
                    }
                }
                log.info("接口订单信息补全,订单数据补全结束:{}",exceptionOrderList);
                if (CollUtil.isNotEmpty(orderItemProductSkus)){
                    iOrderItemProductSkuService.saveBatch(orderItemProductSkus);
                }
                if(CollUtil.isNotEmpty(orderItemPriceList)){
                    iOrderItemPriceService.saveBatch(orderItemPriceList);
                }
                if(CollUtil.isNotEmpty(orderItemNewList)){
                    orderItemService.updateBatchOrSetNUll(orderItemNewList,codesMap);
                }
                if(CollUtil.isNotEmpty(updateOrderList)){
                    iOrdersService.updateBatchOrSetNull(updateOrderList,codesMap);
                }
                if(CollUtil.isNotEmpty(orderLogisticsInfos)){
                    iOrderLogisticsInfoService.updateBatchOrSetNull(orderLogisticsInfos,codesMap);
                }
                offset += pageSize;
            }while (!exceptionOrderList.isEmpty());
        }catch (Exception e){
            log.error("接口订单信息补全异常: {}",e.getMessage());
            throw new RuntimeException();
        }
        // 支付逻辑
        try {
            if(CollUtil.isNotEmpty(orderNoList)) {
                // 去除amazonVC订单
                if (CollUtil.isNotEmpty(amazonVCOrderList)) {
                    List<String> amazonVCOrderNoList = amazonVCOrderList.stream().map(Orders::getOrderNo)
                                                                        .collect(Collectors.toSet()).stream()
                                                                        .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(amazonVCOrderNoList)) {
                        orderNoList.removeAll(amazonVCOrderNoList);
                    }
                }
                if (CollUtil.isNotEmpty(orderNoList)) {
                    List<Orders> ordersList;
                    int offsetOrder = 0;
                    int pageSizeOrder = 30;
                    do {
                        ordersList = iOrdersService.queryByOrderNoListPage(pageSizeOrder, offsetOrder, orderNoList);
                        List<Orders> ordersNeedPayList = null;
                        if(CollUtil.isNotEmpty(ordersList)){
                            // 订单自动支付检查
                            for(Orders orders: ordersList){
                                if(ZSMallSystemEventUtils.checkWalletAutoPaymentEvent(orders.getTenantId(),orders.getCurrency())){
                                    ordersNeedPayList.add(orders);
                                }
                            }
                            if(CollUtil.isNotEmpty(ordersNeedPayList)) {
                                orderPayChain(tenantId, ordersNeedPayList, true, true);
                            }
                        }
                        offsetOrder += pageSizeOrder;
                    } while (!ordersList.isEmpty());
                }
            }
        }catch (Exception e){
            log.error("接口订单信息补全出现异常：{}",e.getMessage());
        }
        // 仓库映射判断
        checkOrdersWarehouse(amazonVCOrderList);
        // amazonVC订单获取面单
        sendAmazonVCGetLogisticsAttachmentMessage(amazonVCOrderList);
    }

    /**
     * 发送订单信息到物流附件队列
     * @param ordersList
     */
    @InMethodLog("发送订单信息到物流附件队列")
    public void sendAmazonVCGetLogisticsAttachmentMessage(List<Orders> ordersList){
        try{
            if (CollUtil.isEmpty(ordersList)){
                return;
            }
            for (Orders ordersSource : ordersList){

                if(ObjectUtil.isEmpty(ordersSource.getChannelOrderNo()) ||
                    !OrderSourceEnum.INTERFACE_ORDER.getValue().equals(ordersSource.getOrderSource())){
                    log.info("非接口订单，不获取面单:{}", ordersSource.getOrderNo());
                    continue;
                }
                List<Orders> channelOrderNoList = iOrdersService.findByChannelOrderNoIn(Arrays.asList(ordersSource.getChannelOrderNo()));
                channelOrderNoList.stream().filter(order -> order.getOrderSource().equals(OrderSourceEnum.INTERFACE_ORDER.getValue()))
                                                         .collect(Collectors.toList());
                AmazonVCOrderLogisticsAttachmentDTO shippingLabelsSend = new AmazonVCOrderLogisticsAttachmentDTO();
                List<AmazonVCOrderLogisticsAttachmentItemDTO> itemDTOList = new ArrayList<>();
                Boolean dealFalg = Boolean.TRUE;
                for (Orders orders : channelOrderNoList) {
                    if(null == orders.getExceptionCode() || orders.getExceptionCode().equals(OrderExceptionEnum.normal.getValue())
                        || orders.getExceptionCode().equals(OrderExceptionEnum.out_of_stock_exception.getValue()) || orders.getExceptionCode().equals(OrderExceptionEnum.logistics_attachment_exception.getValue())){
                        if(ChannelTypeEnum.EC.equals(orders.getChannelType()) || ChannelTypeEnum.Amazon_VC.equals(orders.getChannelType())) {
                            if (LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())) {
                                LogisticsTypeEnum logisticsType = orders.getLogisticsType();
                                OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orders.getOrderNo());
                                if (ObjectUtil.isEmpty(orderLogisticsInfo)) {
                                    log.info("订单的物流信息为空，不能发送获取面单信息:{}", com.alibaba.fastjson.JSONObject.toJSONString(orders.getOrderNo()));
                                    dealFalg = Boolean.FALSE;
                                    continue;
                                }
                                String logisticsAccount = orderLogisticsInfo.getLogisticsAccount();
                                if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                                    if (StrUtil.isEmpty(logisticsAccount)) {
                                        OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orders.getOrderNo(), OrderAttachmentTypeEnum.ShippingLabel);
                                        if (ObjectUtil.isNotEmpty(orderAttachment)) {
                                            log.info("订单已经有发货面单，不发送获取面单信息:{}，面单信息:{}", com.alibaba.fastjson.JSONObject.toJSONString(orders.getOrderNo()), orderAttachment);
                                            dealFalg = Boolean.FALSE;
                                            continue;
                                        }
                                    }
                                }
                                log.info("发送订单信息到物流附件队列");
                                shippingLabelsSend.setOrderNo(orders.getChannelOrderNo())
                                                  .setChannel(orders.getChannelAlias().replaceAll("^[^:]*:", ""))
                                                  .setContainerType("Carton");
                                if (orders.getChannelType().equals(ChannelTypeEnum.EC)) {
                                    shippingLabelsSend.setWarehouseCode(orders.getChannelWarehouseCode());
                                }
                                List<OrderItem> orderItemList = iOrderItemService.getListByOrderId(orders.getId());
                                for (OrderItem orderItem : orderItemList) {
                                    AmazonVCOrderLogisticsAttachmentItemDTO itemDTO = new AmazonVCOrderLogisticsAttachmentItemDTO();
                                    itemDTO.setDeliverOrderNo(orders.getOrderNo()).setDeliverOrderId(orders.getId())
                                           .setChannelOrderItemId(orders.getLineOrderItemId()).setQuantity(1);
                                    ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuCode(orderItem.getProductSkuCode());
                                    if (null != productSkuDetail) {
                                        // 设置长宽高
                                        switch (productSkuDetail.getPackLengthUnit()) {
                                            case foot:
                                                itemDTO.setLength(productSkuDetail.getPackLength());
                                                itemDTO.setWidth(productSkuDetail.getPackWidth());
                                                itemDTO.setHeight(productSkuDetail.getPackHeight());
                                                break;
                                            case inch:
                                                itemDTO.setLength(UnitConverter.inchesToFeet(productSkuDetail.getPackLength()));
                                                itemDTO.setWidth(UnitConverter.inchesToFeet(productSkuDetail.getPackWidth()));
                                                itemDTO.setHeight(UnitConverter.inchesToFeet(productSkuDetail.getPackHeight()));
                                                break;
                                            case m:
                                                itemDTO.setLength(UnitConverter.metersToFeet(productSkuDetail.getPackLength()));
                                                itemDTO.setWidth(UnitConverter.metersToFeet(productSkuDetail.getPackWidth()));
                                                itemDTO.setHeight(UnitConverter.metersToFeet(productSkuDetail.getPackHeight()));
                                                break;
                                            case cm:
                                                itemDTO.setLength(UnitConverter.metersToFeet(UnitConverter.centimetersToMeters(productSkuDetail.getPackLength())));
                                                itemDTO.setWidth(UnitConverter.metersToFeet(UnitConverter.centimetersToMeters(productSkuDetail.getPackWidth())));
                                                itemDTO.setHeight(UnitConverter.metersToFeet(UnitConverter.centimetersToMeters(productSkuDetail.getPackHeight())));
                                                break;
                                        }
                                        // 设置重量
                                        switch (productSkuDetail.getPackWeightUnit()) {
                                            case lb:
                                                itemDTO.setWeight(productSkuDetail.getPackWeight());
                                                break;
                                            case kg:
                                                itemDTO.setWeight(UnitConverter.kilogramsToPounds(productSkuDetail.getPackWeight()));
                                                break;
                                            case g:
                                                itemDTO.setWeight(UnitConverter.gramsToPounds(productSkuDetail.getPackWeight()));
                                                break;
                                            case t:
                                                itemDTO.setWeight(UnitConverter.kilogramsToPounds(UnitConverter.tonsToKilograms(productSkuDetail.getPackWeight())));
                                                break;
                                        }
                                    }
                                    itemDTOList.add(itemDTO);
                                }
                            }
                        }
                    }else{
                        log.info("订单此异常状态下无需推送，订单号:{},异常状态：{}", orders.getOrderNo(),orders.getExceptionCode());
                        dealFalg = Boolean.FALSE;
                    }
                }
                if (dealFalg) {
                    shippingLabelsSend.setItems(itemDTOList);
                    String str = JSON.toJSONString(shippingLabelsSend);
                    String messageId = IdUtil.fastSimpleUUID();
                    Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
                    log.info("[映射触发发送订单信息到物流附件队列]成功，发送参数：{}",JSON.toJSON(shippingLabelsSend));
                    rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_ROUTING_KEY,message);
                } else {
                    log.info("[映射触发发送订单信息到物流附件队列]失败，发送参数：{}",JSON.toJSON(shippingLabelsSend));
                }

            }
        }catch (Exception e){
            log.error("映射触发发送订单信息到物流附件队列报错:{}",e.getMessage());
        }
    }

    /**
     * 检查订单仓库映射
     * @param ordersList
     */
    @InMethodLog(value = "检查订单仓库映射")
    public void checkOrdersWarehouse(List<Orders> ordersList){
        try{
            if (CollUtil.isEmpty(ordersList)){
                return;
            }
            for (Orders orders : ordersList){
                // 检查渠道仓库映射
                if(null == orders.getExceptionCode() || orders.getExceptionCode().equals(OrderExceptionEnum.normal.getValue()) || orders.getExceptionCode().equals(OrderExceptionEnum.out_of_stock_exception.getValue()) || orders.getExceptionCode().equals(OrderExceptionEnum.order_pay_exception.getValue())){
                    if(ChannelTypeEnum.EC.equals(orders.getChannelType()) || ChannelTypeEnum.Amazon_VC.equals(orders.getChannelType())) {
                        if (LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType()) && 1 == orders.getOrderSource()) {
                            ChannelWarehouseInfo channelWarehouseInfo = iChannelWarehouseService.queryByTenantSaleChannelIdAndChannelWarehouseCode(orders.getChannelId(), orders.getChannelWarehouseCode());
                            if (ObjectUtil.isEmpty(channelWarehouseInfo)) {
                                orders.setExceptionCode(OrderExceptionEnum.warehouse_mapping_exception.getValue());
                                iOrdersService.updateById(orders);
                            } else {
                                List<OrderItem> orderItemList = iOrderItemService.getListByOrderId(orders.getId());
                                // 查询供应商仓库是否存在
                                for (OrderItem orderItem : orderItemList) {
                                    Warehouse warehouse = iWarehouseService.queryByTenantIdAndWarehouseCodeAndWarehouseType(orderItem.getSupplierTenantId(), channelWarehouseInfo.getWarehouseCode(), WarehouseTypeEnum.BizArk);
                                    if (ObjectUtil.isEmpty(warehouse)) {
                                        orders.setExceptionCode(OrderExceptionEnum.warehouse_mapping_exception.getValue());
                                        iOrdersService.updateById(orders);
                                    } else {
//                                        List<OrderItemProductSku> orderItemProductSkus = iOrderItemProductSkuService.queryListByOrderNo(orders.getOrderNo());
//                                        if (CollUtil.isNotEmpty(orderItemProductSkus)) {
//                                            for (OrderItemProductSku orderItemProductSku : orderItemProductSkus) {
//                                                orderItemProductSku.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
//                                                orderItemProductSku.setSpecifyWarehouse(warehouse.getWarehouseSystemCode());
//                                            }
//                                            iOrderItemProductSkuService.updateBatchById(orderItemProductSkus);
//                                            // 重新计算订单价格
//                                            computeOrderPrice(Arrays.asList(orders));
//                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("发送订单信息到物流附件队列报错:{}",e.getMessage());
        }
    }

    /**
     * 检查单个订单仓库映射
     * @param orders
     */
    @InMethodLog(value = "检查单个订单仓库映射")
    public void checkOrderWarehouse(Orders orders){
        try{
            if (ObjectUtil.isEmpty(orders)){
                return;
            }
            // 检查渠道仓库映射
            if(null == orders.getExceptionCode() || orders.getExceptionCode().equals(OrderExceptionEnum.normal.getValue())){
                if(ChannelTypeEnum.EC.equals(orders.getChannelType()) || ChannelTypeEnum.Amazon_VC.equals(orders.getChannelType())) {
                    if (LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())) {
                        ChannelWarehouseInfo channelWarehouseInfo = iChannelWarehouseService.queryByTenantSaleChannelIdAndChannelWarehouseCode(orders.getChannelId(), orders.getChannelWarehouseCode());
                        if (ObjectUtil.isEmpty(channelWarehouseInfo)) {
                            orders.setExceptionCode(OrderExceptionEnum.warehouse_mapping_exception.getValue());
                            iOrdersService.updateById(orders);
                        } else {
                            List<OrderItem> orderItemList = iOrderItemService.getListByOrderId(orders.getId());
                            // 查询供应商仓库是否存在
                            for (OrderItem orderItem : orderItemList) {
                                Warehouse warehouse = iWarehouseService.queryByTenantIdAndWarehouseCodeAndWarehouseType(orderItem.getSupplierTenantId(), channelWarehouseInfo.getWarehouseCode(), WarehouseTypeEnum.BizArk);
                                if (ObjectUtil.isEmpty(warehouse)) {
                                    orders.setExceptionCode(OrderExceptionEnum.warehouse_mapping_exception.getValue());
                                    iOrdersService.updateById(orders);
                                } else {
                                    List<OrderItemProductSku> orderItemProductSkus = iOrderItemProductSkuService.queryListByOrderNo(orders.getOrderNo());
                                    if (CollUtil.isNotEmpty(orderItemProductSkus)) {
                                        for (OrderItemProductSku orderItemProductSku : orderItemProductSkus) {
                                            orderItemProductSku.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
                                            orderItemProductSku.setSpecifyWarehouse(warehouse.getWarehouseSystemCode());
                                        }
                                        iOrderItemProductSkuService.updateBatchById(orderItemProductSkus);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("发送订单信息到物流附件队列报错:{}",e.getMessage());
        }
    }

    /**
     * 计算订单价格
     * @param ordersList
     */
    @InMethodLog("计算订单价格")
    public void computeOrderPrice(List<Orders> ordersList){
        try {
            for(Orders orders : ordersList){
                String warehouseSystemCode = null;
                String warehouseCode = null;
                List<OrderItemPrice> itemPrices = new ArrayList<>();
                List<OrderItem> orderItemUpdateList = new ArrayList<>();
                List<OrderItem> orderItemList = TenantHelper.ignore(()->iOrderItemService.getListByOrderId(orders.getId()));
                OrderAddressInfo orderAddressInfo = TenantHelper.ignore(()->iOrderAddressInfoService.getByOrderNo(orders.getOrderNo()));
                OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderId(orders.getId());
                List<OrderItemProductSku> orderItemProductSkus = iOrderItemProductSkuService.queryListByOrderNo(orders.getOrderNo());
                if(CollUtil.isNotEmpty(orderItemProductSkus)){
                    for (OrderItemProductSku orderItemProductSku : orderItemProductSkus){
                        Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCode(orderItemProductSku.getWarehouseSystemCode());
                        if(ObjectUtil.isNotEmpty(warehouse)){
                            warehouseCode = warehouse.getWarehouseCode();
                        }
                    }
                }
                // 仓库不存在，不计算订单价格
                if(StringUtils.isEmpty(warehouseCode)){
                    continue;
                }
                iOrderItemProductSkuService.updateBatchById(orderItemProductSkus);
                if(CollUtil.isNotEmpty(orderItemList) && ObjectUtil.isNotEmpty(orderAddressInfo) && ObjectUtil.isNotEmpty(orderLogisticsInfo)){
                    for(OrderItem orderItem : orderItemList){
                        // 订单活动判断
                        // 获取商品相关的活动信息
                        DistributorProductActivity distributorProductActivity = orderActivitySupport.getDistributorActivityByStock(orderItem.getTenantId(), orderItem.getProductSkuCode(), orderItem.getCountryCode(),orderActivitySupport.logisticsTypeConvert(orders.getLogisticsType()));
                        // 判断活动订单
                        if (null != distributorProductActivity) {
                            orderItem.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                            orderItem.setActivityType(distributorProductActivity.getActivityType());
                            orders.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                            orders.setActivityType(distributorProductActivity.getActivityType());
                        }
                        OrderPriceCalculateDTO orderPriceCalculateDTO = new OrderPriceCalculateDTO();
                        orderPriceCalculateDTO.setOrderItem(orderItem);
                        orderPriceCalculateDTO.setLogisticsType(orders.getLogisticsType());
                        orderPriceCalculateDTO.setActivityCode(orderItem.getActivityCode());
                        List<String> stashList = Arrays.asList(warehouseCode);
                        // 仓库编号入口
                        LocaleMessage calculationOrderItemPriceExceptionMessage = priceSupportV2.calculationOrderItemPrice(orderPriceCalculateDTO,orders.getTenantId(),orderAddressInfo.getZipCode(),stashList, orders, THIRD_CREATE_ORDER, orderLogisticsInfo.getLogisticsCompanyName());
                        warehouseSystemCode = orderPriceCalculateDTO.getWarehouseSystemCode();
                        if(ObjectUtil.isNotEmpty(calculationOrderItemPriceExceptionMessage) && StringUtils.isNotEmpty(calculationOrderItemPriceExceptionMessage.getEn_US())){
                            orders.setPayErrorMessage(calculationOrderItemPriceExceptionMessage.toJSON());
                        }
                        OrderItemPrice orderItemPrice1 = orderPriceCalculateDTO.getOrderItemPrice();
                        OrderItemPrice orderItemPrice = new OrderItemPrice();
                        BeanUtils.copyProperties(orderItemPrice1, orderItemPrice);
                        orderItemPrice.setTotalQuantity(orderItem.getTotalQuantity()).setOrderItemId(orderItem.getId()).setOrderItemNo(orderItem.getOrderItemNo()).setProductSkuCode(orderItem.getProductSkuCode()).setLogisticsType(orderItem.getLogisticsType());
                        itemPrices.add(orderItemPrice);
                        OrderItem orderItemUpdate = new OrderItem();
                        BeanUtils.copyProperties(orderItem, orderItemUpdate);
                        orderItemUpdateList.add(orderItemUpdate);
                    }
                }
                // 重新计算主订单数据
                priceSupportV2.recalculateOrderAmount(orders,itemPrices);
                // 更新订单明细信息
                orderItemPriceService.saveUpdateOrSetNUll(itemPrices,orders.getExceptionCode());
                orderItemService.updateOrSetNUll(orderItemUpdateList,orders.getExceptionCode());
                // orders 需要二次更新,第一次更新基础信息,第二次更新价格信息
                iOrdersService.saveOrUpdate(orders);
                iOrderItemProductSkuService.updateOrSetNull(orders.getOrderNo(),warehouseSystemCode,warehouseSystemCode);
            }
        }catch (Exception e){
            log.error("计算订单价格报错:{}",e.getMessage());
        }
    }

    /**
     * 功能描述：获取仓库清单,支持不同子单号的产品维度的仓库清单,无视租户
     * v2 如果查不到仓库随意给仓库 不走库存不足
     * @param orderItemList 订单项目列表
     * @return {@link HashMap }<{@link String }, {@link List }<{@link String }>>
     * <AUTHOR>
     * @date 2024/08/02
     */
//    public HashMap<String, List<String>> getStashList(List<OrderItem> orderItemList) {
//        HashMap<String, List<String>> stringHashMap = new HashMap<>();
//        for (OrderItem item : orderItemList) {
//            LambdaQueryWrapper<ProductSkuStock> eq = new LambdaQueryWrapper<ProductSkuStock>()
//                .eq(ProductSkuStock::getProductSkuCode, item.getProductSkuCode())
//                .ge(ProductSkuStock::getStockAvailable, item.getTotalQuantity())
//                .eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid)
//                .eq(ProductSkuStock::getDelFlag, 0);
//            List<ProductSkuStock> list = TenantHelper.ignore(()->iProductSkuStockService.list(eq));
//            List<String> collect = list.stream().map(ProductSkuStock::getWarehouseSystemCode)
//                                       .collect(Collectors.toList());
//            if(CollUtil.isNotEmpty(collect)){
//                LambdaQueryWrapper<Warehouse> in = new LambdaQueryWrapper<Warehouse>().in(Warehouse::getWarehouseSystemCode, collect);
//                List<Warehouse> list1 = TenantHelper.ignore(()->iWarehouseService.list(in));
//                List<String> collect1 = list1.stream().map(Warehouse::getWarehouseCode)
//                                             .collect(Collectors.toList());
//                stringHashMap.put(item.getOrderItemNo(),collect1);
//            }
//
//        }
//
//        return stringHashMap;
//    }

//    v2 如果查不到仓库随意给仓库 不走库存不足
    public HashMap<String, List<String>> getStashList(List<OrderItem> orderItemList) {
        HashMap<String, List<String>> stringHashMap = new HashMap<>();
        for (OrderItem item : orderItemList) {
            String wareSystemCode=null;
            LambdaQueryWrapper<OrderItemProductSku> oo = new LambdaQueryWrapper<>();
            oo.eq(OrderItemProductSku::getOrderNo,item.getOrderNo());
            OrderItemProductSku byOrderItemId =TenantHelper.ignore(()->iOrderItemProductSkuService.getOne(oo));
            if (ObjectUtil.isNotNull(byOrderItemId) ){
                wareSystemCode=byOrderItemId.getWarehouseSystemCode();
            }else {
                LambdaQueryWrapper<OrderImportTemp> o = new LambdaQueryWrapper<>();
                o.eq(OrderImportTemp::getOrderNo,item.getOrderNo());
                OrderImportTemp orderImportTemp = TenantHelper.ignore(() -> iOrderImportTempService.getOne(o));
                if (ObjectUtil.isNotNull(orderImportTemp)){
                    wareSystemCode=orderImportTemp.getWarehouseSystemCode();
                }
            }
            Orders order = TenantHelper.ignore(() -> iOrdersService.getByOrderNo(item.getOrderNo()));
            //判断当前订单的物流类型，如果是自提,使用客户指定的,目前除了抓单
            if (ObjectUtil.isNotNull(order)&&ObjectUtil.equal(item.getLogisticsType().name(),LogisticsTypeEnum.PickUp.name())&&
                ObjectUtil.notEqual(order.getOrderSource(),OrderSourceEnum.INTERFACE_ORDER.getValue()) &&
                StrUtil.isNotBlank(wareSystemCode)){
                //将系统编码转编码
                Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCode(wareSystemCode);
                if (ObjectUtil.isNotNull(warehouse)){
                    stringHashMap.put(item.getOrderItemNo(),List.of(warehouse.getWarehouseCode()));
                }
            }else {
                // 活动订单逻辑
                if (ObjectUtil.isNotNull(item.getActivityCode())){
                    // 根据活动编号和订单数量获取活动可用仓库信息
                    List<DistributorProductActivityStock> activityAvailableWarehouseInfoList = orderActivitySupport.getActivityWarehouseInfo(item.getActivityCode(), item.getTotalQuantity());
                    if(CollUtil.isNotEmpty(activityAvailableWarehouseInfoList)){
                        List<String> activityStockWarehouseCode = activityAvailableWarehouseInfoList.stream().map(DistributorProductActivityStock::getWarehouseCode).collect(Collectors.toList());
                        if(CollUtil.isNotEmpty(activityStockWarehouseCode)){
                            stringHashMap.put(item.getOrderItemNo(),activityStockWarehouseCode);
                        }
                    }
                    continue;
                }
                //如果代发指定了仓库
                if (StrUtil.isNotBlank(wareSystemCode)){
                    Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCode(wareSystemCode);
                    if (ObjectUtil.isNotNull(warehouse)){
                        //判断代发指定的仓库是否有库存
                        boolean orderHaveStock = getOrderHaveStock(item.getProductSkuCode(), item.getTotalQuantity(), warehouse.getWarehouseSystemCode());
                        if (orderHaveStock){
                            stringHashMap.put(item.getOrderItemNo(),List.of(warehouse.getWarehouseCode()));
                        }else {
                            List<String> ordersInStockWarehouseCode = getOrdersInStockWarehouseCode(item.getProductSkuCode(), item.getTotalQuantity());
                            stringHashMap.put(item.getOrderItemNo(),ordersInStockWarehouseCode);
                        }
                    }
                }else {
                    List<String> ordersInStockWarehouseCode = getOrdersInStockWarehouseCode(item.getProductSkuCode(), item.getTotalQuantity());
                    stringHashMap.put(item.getOrderItemNo(),ordersInStockWarehouseCode);
                }
            }
        }
        return stringHashMap;
    }

    /**
     * 获取订单关联商品有库存的所有仓库
     * @param productSkuCode 商品编码
     * @param totalQuantity 数量
     * @return
     */
    public List<String> getOrdersInStockWarehouseCode(String productSkuCode,Integer totalQuantity){
        LambdaQueryWrapper<ProductSkuStock> eq = new LambdaQueryWrapper<ProductSkuStock>()
            .eq(ProductSkuStock::getProductSkuCode, productSkuCode)
            .eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid)
            .ge(ProductSkuStock::getStockAvailable,totalQuantity)
            .eq(ProductSkuStock::getDelFlag, 0);
        List<ProductSkuStock> list = TenantHelper.ignore(()->iProductSkuStockService.list(eq));
        List<String> collect = list.stream().map(ProductSkuStock::getWarehouseSystemCode)
                                   .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(collect)){
            LambdaQueryWrapper<Warehouse> in = new LambdaQueryWrapper<Warehouse>().in(Warehouse::getWarehouseSystemCode, collect);
            List<Warehouse> list1 = TenantHelper.ignore(()->iWarehouseService.list(in));
            return list1.stream().map(Warehouse::getWarehouseCode)
                        .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }


    /**
     * 判断订单商品在指定仓库是否有库存
     * @param productSkuCode 商品编码
     * @param totalQuantity 数量
     * @param wareSystemCode 仓库系统编码
     * @return
     */
    public boolean getOrderHaveStock(String productSkuCode,Integer totalQuantity,String wareSystemCode){
        LambdaQueryWrapper<ProductSkuStock> eq = new LambdaQueryWrapper<ProductSkuStock>()
            .eq(ProductSkuStock::getProductSkuCode, productSkuCode)
            .eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid)
            .eq(ProductSkuStock::getWarehouseSystemCode,wareSystemCode)
            .ge(ProductSkuStock::getStockAvailable,totalQuantity)
            .eq(ProductSkuStock::getDelFlag, 0);
        ProductSkuStock productSkuStock = TenantHelper.ignore(()->iProductSkuStockService.getBaseMapper().selectOne(eq));
        return !ObjectUtil.isNull(productSkuStock);
    }
    public <T> Map<Integer, List<T>> splitListIntoGroups(List<T> list, int size) {
        Map<Integer, List<T>> result = new HashMap<>();
        for (int i = 0; i < list.size(); i += size) {
            int groupKey = i / size; // 使用整除来生成组键
            int end = Math.min(i + size, list.size()); // 确保不会超出列表长度
            List<T> subList = list.subList(i, end);
            result.put(groupKey, subList);
        }
        return result;
    }
    /**
     * 设置 orderItemProductSku 数据
     *
     * @param orderItemProductSku
     * @param orderItem
     * @param productMapping
     * @return
     */
    public OrderItemProductSku setOrderItemProductSku(OrderItemProductSku orderItemProductSku, OrderItem orderItem,ProductMapping productMapping) {
        orderItemProductSku.setSupplierTenantId(orderItem.getSupplierTenantId());
        orderItemProductSku.setOrderNo(orderItem.getOrderNo());
        if(null != productMapping){
            LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<>();
            lqw.eq(ProductSku::getProductSkuCode, productMapping.getProductSkuCode());

            ProductSku productSku = TenantHelper.ignore(() -> productSkuService.getOne(lqw));
            if(ObjectUtil.isNotNull(productSku)){
                List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSku.getId(), AttachmentTypeEnum.Image);
                if (CollUtil.isNotEmpty(skuAttachmentList) && ObjectUtil.isNotEmpty(skuAttachmentList.get(0))) {
                    ProductSkuAttachment firstImage = skuAttachmentList.get(0);
                    Long ossId = firstImage.getOssId();
                    String attachmentSavePath = firstImage.getAttachmentSavePath();
                    String attachmentShowUrl = firstImage.getAttachmentShowUrl();
                    orderItemProductSku.setImageOssId(ossId);
                    orderItemProductSku.setImageSavePath(attachmentSavePath);
                    orderItemProductSku.setImageShowUrl(attachmentShowUrl);
                }
                Product product = TenantHelper.ignore(() -> productService.getOne(new LambdaQueryWrapper<Product>().eq(Product::getProductCode, productSku.getProductCode())));
                orderItemProductSku.setProductCode(productSku.getProductCode());
                orderItemProductSku.setProductSkuCode(productSku.getProductSkuCode());
                orderItemProductSku.setSku(productSku.getSku());
                orderItemProductSku.setUpc(productSku.getUpc());
                orderItemProductSku.setMappingSku(productSku.getErpSku());
                orderItemProductSku.setProductName(productSku.getName());
                orderItemProductSku.setSpecComposeName(productSku.getSpecComposeName());
                orderItemProductSku.setSpecValName(productSku.getSpecValName());
                if(null != product){
                    orderItemProductSku.setDescription(product.getDescription());
                }
                orderItemProductSku.setErpSku(productSku.getErpSku());
            }
            orderItemProductSku.setOrderItemNo(orderItem.getOrderItemNo());
            orderItemProductSku.setChannelType(ChannelTypeEnum.Temu);
            orderItemProductSku.setChannelId(orderItem.getChannelId());
            orderItemProductSku.setChannelVariantId(null);
            orderItemProductSku.setChannelWarehouseCode(null);
            orderItemProductSku.setTenantId(orderItem.getTenantId());
        }
        return orderItemProductSku;
    }

    public void orderPayChainOnlyErp(String tenantId, List<Orders> orderList, boolean isNeedCreate,
                                     boolean isGenuinePay) throws ProductException, StockException, OrderPayException {

        orderStatusCheck(orderList);
        // 检测订单是否上传标签
        isExpressSheetUpload(tenantId, orderList);
        if(CollUtil.isEmpty(orderList)){
            return;
        }
        List<Orders> pendingOrderList = orderLockToPending(tenantId, orderList);
        //   only erp
        orderPreInspectionOnlyErp(pendingOrderList);
        //   only erp
        orderStockAdjustOnlyErp(pendingOrderList);
        // 钱包支付,但是一旦后续流程发生异常,钱包金额目前并没有回滚
        orderWalletPay(pendingOrderList, isGenuinePay);
        orderUpdateLogistics(pendingOrderList);
        orderThirdWarehouseFollowUp(pendingOrderList, isNeedCreate);
    }
    @SneakyThrows
    private void orderPreInspectionOnlyErp(List<Orders> orderList) {

        // 商品管控提示
        String productControlTips = businessParameterService.getValueFromString(BusinessParameterType.PRODUCT_CONTROL_TIPS);
        AtomicInteger count = new AtomicInteger(0);
        StringBuilder errorTips = new StringBuilder();
        ArrayList<String> orders = new ArrayList<>();
        for (Orders order : orderList) {
            Long orderId = order.getId();
            String orderNo = order.getOrderNo();
            OrderStateType orderState = order.getOrderState();
            LogisticsTypeEnum logisticsType = order.getLogisticsType();
            OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderId(orderId);

            // 不是Pending状态的订单，直接跳过
            if (!OrderStateType.Pending.equals(orderState)) {
                continue;
            }

            List<OrderItemPrice> orderItemPriceList = new ArrayList<>();
            List<OrderItem> orderItemList = iOrderItemService.getListByOrderId(orderId);

            try {
                // 自提且非第三方的订单，需要检查是否上传了快递标签
                if (LogisticsTypeEnum.PickUp.equals(logisticsType) && StrUtil.isBlank(orderLogisticsInfo.getLogisticsAccount())) {
                    Boolean shippingLabelExist = orderLogisticsInfo.getShippingLabelExist();
                    if (!shippingLabelExist) {
                        throw new OrderPayException(ZSMallStatusCodeEnum.TEMP_ORDER_PICK_UP_NOT_EXIST_SHIPPING_LABEL_ERROR);
                    }
                }

                String tenantId = order.getTenantId();
                if (ObjectUtil.isEmpty(order.getChannelType())) {
                    order.setChannelType(ChannelTypeEnum.Others);
                }
                ChannelTypeEnum channelType = order.getChannelType();

                OrderAddressInfo addressInfo = iOrderAddressInfoService.getByOrderNoAndAddressType(orderNo, OrderAddressType.ShipAddress);

//                ChannelOrderInspectionService inspectionService = channelOrderInspectionFactory.getService(channelType);
//                if (inspectionService != null) {
//                    inspectionService.logisticsInspection(order, orderItemList, orderLogisticsInfo);
//                }

                // 目的地国家
                String destCountryCode = addressInfo.getCountryCode();
                List<String> controlProduct = new ArrayList<>();


                for (OrderItem orderItem : orderItemList) {
                    String activityCode = orderItem.getActivityCode();
                    String productSkuCode = orderItem.getProductSkuCode();

                    // 商品是否下架判断
                    Product product = iProductService.queryByProductSkuCode(productSkuCode);
                    if(null != product && null != product.getShelfState() && product.getShelfState().equals(ShelfStateEnum.OffShelf)){
                        throw new OrderPayException(ZSMallStatusCodeEnum.PRODUCT_DELISTED);
                    }
                    // 排除erp
                    if(!ChannelTypeEnum.Erp.equals(order.getChannelType())){
                        if(null != order.getOrderSource() && order.getOrderSource().equals(OrderSourceEnum.INTERFACE_ORDER.getValue())){
                            // 商品是否映射判断，只有渠道订单需要判断
                            ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(orderItem.getTenantId(), order.getChannelId(), orderItem.getChannelSku(), SyncStateEnum.Mapped,order.getCountryCode());
                            if(ObjectUtil.isEmpty(productMapping)){
                                throw new OrderPayException(ZSMallStatusCodeEnum.PRODUCT_MAPPING_EXCEPTION);
                            }
                        }
                    }
                    // 校验商品有效性
                    ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                    if (productSku == null) {
                        throw new OrderPayException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXISTS.args(productSkuCode));
                    }

                    OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemIdNoTenant(orderItem.getId());

                    // 不存在价格规则关联，则提示错误
                    ProductSkuPriceRuleRelation ruleRelation =
                        iProductSkuPriceRuleRelationService.getByProductSkuCode(productSkuCode);
                    if (ruleRelation == null && StrUtil.isBlank(activityCode)) {
                        log.error("商品{}不存在价格规则关联", productSkuCode);
//                        throw new OrderPayException(ZSMallStatusCodeEnum.SYSTEM_ERROR_E10028);
                    }

                    // 判断商品是否受管控
                    boolean allow = iProductChannelControlService.checkUserAllow(tenantId, productSkuCode, channelType.name());
                    if (!allow) {
                        controlProduct.add(productSkuCode);
                        continue;
                    }

                    // 代发才需要判断收货地与发货地是否支持物流 x 测试完成后需要放开
                    if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                    //    this.shippingCountrySupport(activityCode, destCountryCode, productSkuCode, orderItemProductSku.getSpecifyWarehouse());
                    }

                    OrderPriceCalculateDTO dto = new OrderPriceCalculateDTO();
                    dto.setChannelTypeEnum(channelType);
                    dto.setLogisticsType(logisticsType);
                    dto.setOrderItem(orderItem);
                    dto.setCountry(destCountryCode);
                    dto.setActivityCode(activityCode);
                    //  支付完后会根据商品金额进行金额的重写计算
                    LocaleMessage localeMessage = this.calculationOrderItemPrice(dto);
                    if (localeMessage.hasData()) {
                        throw new OrderPayException(localeMessage);
                    } else {
                        //  搜集子订单价格表，等会需要重新统计主订单的总金额
                        orderItemPriceList.add(dto.getOrderItemPrice());
                    }
                }

                if (CollUtil.isNotEmpty(controlProduct)) {
                    String newProductControlTips = StrUtil.replace(productControlTips, "{itemNo}", CollUtil.join(controlProduct, "/"));
                    throw new OrderPayException(LocaleMessage.parseByJSONStr(newProductControlTips));
                }

                // x 重新计算订单金额
                if (ChannelTypeEnum.TikTok.equals(order.getChannelType()) || ChannelTypeEnum.Temu.equals(order.getChannelType())) {

                } else {
                    recalculateOrderAmount(order, orderItemPriceList);
                }
                order.setPayErrorMessage(null);
                orderLogisticsInfo.setLogisticsErrorMessage(null);
            } catch (RStatusCodeException e) {
                orders.add(order.getOrderNo());
                log.info("订单{}前置检查，出现异常（RStatusCodeException） {}", orderNo, e.getMessage(), e);
                order.setOrderState(OrderStateType.Failed);
                JSONObject message = LocaleMessage.byStatusCodeToJSON(e.getStatusCode());
                order.setPayErrorMessage(message);
                order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                orderLogisticsInfo.setLogisticsErrorMessage(message);
                errorTips.append(message.toString()).append(";");
//                throw new Exception(message.toString());
            } catch (OrderPayException e) {
                log.info("订单{}前置检查，出现异常（OrderPayException） {}", orderNo, e.getMessage(), e);
                orders.add(order.getOrderNo());
                JSONObject errorMsg = e.getLocaleMessage().toJSON();
                order.setOrderState(OrderStateType.Failed);
                order.setPayErrorMessage(errorMsg);
                order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                orderLogisticsInfo.setLogisticsErrorMessage(errorMsg);
                e.printStackTrace();
                errorTips.append(errorMsg.toString()).append(";");
//                throw new Exception(errorMsg.toString());
            } catch (Exception e) {
                orders.add(order.getOrderNo());
                log.info("订单{}前置检查，出现未知异常 {}", orderNo, e.getMessage(), e);
                JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_PAY_UNKNOWN_ERROR).toJSON();
                order.setOrderState(OrderStateType.Failed);
                order.setPayErrorMessage(errorMsg);
                order.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
                orderLogisticsInfo.setLogisticsErrorMessage(errorMsg);
                e.printStackTrace();
                // 考虑把throw 去掉
                errorTips.append(errorMsg.toString()).append(";");
//                throw new Exception(errorMsg.toString());
            } finally {
                iOrdersService.updateById(order);
                iOrderItemService.updateBatchById(orderItemList);
                iOrderItemPriceService.updateBatchById(orderItemPriceList);
                iOrderLogisticsInfoService.updateById(orderLogisticsInfo);

                if (OrderStateType.Failed.equals(order.getPayErrorMessage())) {
                    iOrderItemService.changeOrderStateByOrderId(orderId, OrderStateType.Pending, OrderStateType.Failed);
                }
                // count 自增
                count.getAndIncrement();

            }
        }
        if (count.get() == orderList.size() && ObjectUtil.isNotEmpty(errorTips)) {
            LambdaUpdateWrapper<Orders> set = new LambdaUpdateWrapper<Orders>().in(Orders::getOrderNo, orders)
                                                                               .set(Orders::getOrderState, OrderStateType.Failed);
            iOrdersService.update(set);
            throw new RuntimeException(errorTips.toString());
        }
    }
    @SneakyThrows
    private void orderStockAdjustOnlyErp(List<Orders> orderList) throws StockException {
        log.info("准备进行订单库存调整 orderList.size = {}", CollUtil.size(orderList));
        List<String> failOrders = new ArrayList<>();
        boolean result = true;
        StringBuilder orderNos = new StringBuilder();
        ArrayList<String> failOrderList = new ArrayList<>();
        StringBuilder errorTips = new StringBuilder();
        for (Orders order : orderList) {
            Long orderId = order.getId();
            String orderNo = order.getOrderNo();
            OrderStateType orderState = order.getOrderState();
            List<OrderItemProductSku> orderItemProductSkuList = new ArrayList<>();
            List<AdjustStockDTO> dtoList = new ArrayList<>();
            try {
                log.info("主订单ID{}, 订单{}, 状态{}, 开始调整库存", orderId, orderNo, orderState);
                ChannelTypeEnum channelType = order.getChannelType();

                OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderId(orderId);
                List<OrderItem> orderItemList = iOrderItemService.getListByOrderId(orderId);

                String logisticsAccount = orderLogisticsInfo.getLogisticsAccount();
                String logisticsZipCode = orderLogisticsInfo.getLogisticsZipCode();
                String logisticsCountryCode = orderLogisticsInfo.getLogisticsCountryCode();
                LogisticsTypeEnum logisticsType = orderLogisticsInfo.getLogisticsType();

                // 不是Pending状态的订单，直接跳过
                if (!OrderStateType.Pending.equals(orderState)) {
                    continue;
                }

                // 已经调整成功的库存dto集合，出现某一个库存调整失败时，已经调整成功的需要归还库存
                for (OrderItem orderItem : orderItemList) {

                    Long orderItemId = orderItem.getId();
                    String orderItemNo = orderItem.getOrderItemNo();
                    log.info("子订单ID{} 子订单{}开始调整库存", orderItemId, orderItemNo);

                    String activityCode = orderItem.getActivityCode();
                    String productSkuCode = orderItem.getProductSkuCode();
                    Integer totalQuantity = orderItem.getTotalQuantity();
                    OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItemId);

                    // 指定仓库
                    String specifyWarehouse = orderItemProductSku.getSpecifyWarehouse();
                    AdjustStockDTO dto = new AdjustStockDTO();
                    dto.setProductSkuCode(productSkuCode);
                    dto.setLogisticsType(logisticsType);
                    dto.setActivityCode(activityCode);
                    // 负数时为减少库存
                    dto.setAdjustQuantity(totalQuantity * -1);
                    dto.setDestCountry(logisticsCountryCode);
                    dto.setDestZipCode(logisticsZipCode);
                    dto.setSpecifyWarehouse(specifyWarehouse);
                    dto.setLogisticsAccount(StrUtil.isNotBlank(logisticsAccount));

                    if (StrUtil.isNotBlank(activityCode)) {
                        String warehouseSystemCode = ZSMallActivityEventUtils.activityStockAdjust(dto);
                        orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);
                        orderItemProductSkuList.add(orderItemProductSku);

                        // 加入调整成功的数组中
                        dtoList.add(dto.setSpecifyWarehouse(warehouseSystemCode));
                    } else {
                        // 通过锁机制统一减库存
                        String warehouseSystemCode ;
                        if(ChannelTypeEnum.Erp.equals(order.getChannelType())){
                            warehouseSystemCode = productSkuStockService.adjustStockOnlyErp(dto, orderNo);
                        }else {
                            warehouseSystemCode = productSkuStockService.adjustStock(dto, orderNo);
                        }

                        orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);
                        orderItemProductSkuList.add(orderItemProductSku);

                        // 加入调整成功的数组中
                        dtoList.add(dto.setSpecifyWarehouse(warehouseSystemCode));
                    }
                }
            } catch (StockException e) {
                log.info("订单{}库存调整出现异常（StockException） {}", orderNo, e.getMessage(), e);
                e.printStackTrace();
                LocaleMessage localeMessage = e.getLocaleMessage();
                JSONObject errorMsg = localeMessage.toJSON();
                order.setPayErrorMessage(errorMsg);
                order.setOrderState(OrderStateType.Failed);
                order.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                result = false;
                failOrders.add(order.getOrderNo());
                errorTips.append("订单库存调整出现异常（StockException）" + orderNo + e.getMessage()).append(";");
//                    throw new StockException("订单库存调整出现异常（StockException）" + orderNo + e.getMessage());
            } catch (Exception e) {

                log.info("订单{}库存调整出现未知异常 {}", orderNo, e.getMessage(), e);
                JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_STOCK_ADJUST_UNKNOWN_ERROR)
                                                   .toJSON();
                order.setPayErrorMessage(errorMsg);
                order.setOrderState(OrderStateType.Failed);
                order.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                result = false;
                failOrders.add(order.getOrderNo());
                errorTips.append("订单库存调整出现异常（StockException）" + orderNo + e.getMessage()).append(";");
//                    throw new StockException("订单库存调整出现异常（StockException）" + orderNo + e.getMessage());
            } finally {
                iOrdersService.updateById(order);
                OrderStateType finalState = order.getOrderState();
                if (OrderStateType.Failed.equals(finalState)) {
                    iOrderItemService.changeOrderStateByOrderId(orderId, OrderStateType.Pending, OrderStateType.Failed);
                    // 归还已占用的库存
                    adjustStockDTORestock(dtoList);
                } else {
                    iOrderItemProductSkuService.updateBatchById(orderItemProductSkuList);
                }
                log.info("主订单ID{} 订单{}库存调整完成，最终状态 {}", orderId, orderNo, finalState);

            }


        }
        if (!result) {
            // 此处出现问题后,实际还是有订单还未支付 处于Pending状态 ,这些订单实际应该流入支付流程 ,所以此处需要筛选
            if (orderList.size() > 1) {
                // orderList中筛选掉failOrders集合中的元素
                orderList = orderList.stream().filter(a -> !failOrders.contains(a.getOrderNo()))
                                     .collect(Collectors.toList());
            } else {
                // 订单的状态集体改为失败
                throw new StockException("订单库存调整出现异常（StockException）,任务中断,异常信息:" + errorTips.toString());
            }

        }
    }

    /**
     * amazonVC订单面单上传处理
     * @param amazonVCOrderLogisticsAttachmentCallBackDTO
     */
    @InMethodLog("订单面单上传处理方法")
    public void amazonVCOrderAttachmentCallBack(AmazonVCOrderLogisticsAttachmentCallBackDTO amazonVCOrderLogisticsAttachmentCallBackDTO) {
        // 错误情况下设置订单为获取面单异常
         if(null != amazonVCOrderLogisticsAttachmentCallBackDTO && CollUtil.isEmpty(amazonVCOrderLogisticsAttachmentCallBackDTO.getItems())){
             String channelOrderNo = amazonVCOrderLogisticsAttachmentCallBackDTO.getOrderNo();
             if(StringUtils.isNotEmpty(channelOrderNo)){
                 List<Orders> ordersList = iOrdersService.findByChannelOrderNoIn(Arrays.asList(channelOrderNo));
                 if(CollUtil.isNotEmpty(ordersList)){
                     for(Orders orders:ordersList){
                         OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orders.getOrderNo(), OrderAttachmentTypeEnum.ShippingLabel);
                         if(null == orderAttachment){
                             orders.setExceptionCode(OrderExceptionEnum.logistics_attachment_exception.getValue());
                         }
                     }
                     iOrdersService.updateBatchById(ordersList);
                 }
             }
         }
        if(null != amazonVCOrderLogisticsAttachmentCallBackDTO && CollUtil.isNotEmpty(amazonVCOrderLogisticsAttachmentCallBackDTO.getItems())){
            log.info("订单面单信息:{}", JSONUtil.toJsonStr(amazonVCOrderLogisticsAttachmentCallBackDTO));
            List<AmazonVCOrderLogisticsAttachmentCallBackItemDTO> items = amazonVCOrderLogisticsAttachmentCallBackDTO.getItems();
            List<Orders> ordersList = iOrdersService.queryListByIds(amazonVCOrderLogisticsAttachmentCallBackDTO.getItems().stream()
                                                                                                            .map(item -> Long.parseLong(item.getDeliverOrderId())).collect(Collectors.toSet()));
            Map<Long, Orders> orderMap = ordersList.stream()
                .collect(Collectors.toMap(Orders::getId, order -> order));

            if (CollectionUtils.isEmpty(ordersList)) {
                //log.error("根据DeliverOrderId查询不到订单信息，DeliverOrderId:{},amazonVC订单处理面试失败:{}", amazonVCOrderLogisticsAttachmentCallBackItemDTO.getDeliverOrderId(),amazonVCOrderLogisticsAttachmentCallBackDTO);
                return;
            }
            for (AmazonVCOrderLogisticsAttachmentCallBackItemDTO item : items) {
                Orders orders = orderMap.get(Long.valueOf(item.getDeliverOrderId()));
                if(null == orders){
                    log.error("根据DeliverOrderId查询不到订单信息，DeliverOrderId:{},订单处理面试失败:{}", item.getDeliverOrderId(),amazonVCOrderLogisticsAttachmentCallBackDTO);
                    return;
                }
                if("Paid".equals(orders.getOrderState())){
                    log.error("订单已支付，无需回传，DeliverOrderId:{},订单处理面试失败:{}", item.getDeliverOrderId(),amazonVCOrderLogisticsAttachmentCallBackDTO);
                    return;
                }
                shippingLabelDeal(item, orders);
            }

        }
    }

    private void shippingLabelDeal(
        AmazonVCOrderLogisticsAttachmentCallBackItemDTO amazonVCOrderLogisticsAttachmentCallBackItemDTO,
        Orders orders) {
        SysOssVo sysOssVo = sysOssService.downloadAndUploadFileNotAsync(amazonVCOrderLogisticsAttachmentCallBackItemDTO.getFileUrl(), amazonVCOrderLogisticsAttachmentCallBackItemDTO.getDeliverOrderId(), amazonVCOrderLogisticsAttachmentCallBackItemDTO.getTrackingNumber(),"pdf");
        if(ObjectUtil.isEmpty(sysOssVo)){
            if(null != orders){
                OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orders.getOrderNo(), OrderAttachmentTypeEnum.ShippingLabel);
                if(null == orderAttachment){
                    orders.setExceptionCode(OrderExceptionEnum.logistics_attachment_exception.getValue());
                }
                iOrdersService.updateNoTenant(orders);
            }
            log.error("订单面单信息异常:"+ amazonVCOrderLogisticsAttachmentCallBackItemDTO.getFileUrl());
            return;
        }
        OrderUpdateBo bo = new OrderUpdateBo(orders.getOrderNo(), null, true, sysOssVo);
        iOrdersService.uploadShippingLabel(bo);
        List<OrderItem> listByOrderId = iOrderItemService.getListByOrderId(orders.getId());
        OrderItem orderItem = new OrderItem();
        if(CollUtil.isNotEmpty(listByOrderId)){
             orderItem = listByOrderId.get(0);
        }
        if(ObjectUtil.isNull(orderItem)){
            log.error("订单面单上传处理方法,根据订单ID查询不到订单详情信息:{}", orders.getId());
            return;
        }
        OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
        if(ObjectUtil.isNull(orderItemProductSku)){
            log.error("订单面单上传处理方法,根据订单ID查询不到订单ProductSku信息:{}", orders.getId());
            return;
        }
        OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderId(orders.getId());
        if(ObjectUtil.isNull(orderLogisticsInfo)){
            log.error("订单面单上传处理方法,根据订单ID查询不到订单LogisticsInfo信息:{}", orders.getId());
            return;
        }
        OrderAddressInfo orderAddressInfo = iOrderAddressInfoService.getByOrderNo(orders.getOrderNo());
        if(ObjectUtil.isEmpty(orderAddressInfo)){
            log.error("订单面单上传处理方法,根据订单ID查询不到订单AddressInfo信息:{}", orders.getId());
            return;
        }
        // 订单tracking信息处理
        List<OrderItemTrackingRecord> trackingList = new ArrayList<>();
        String trimTracking = StrUtil.trim(amazonVCOrderLogisticsAttachmentCallBackItemDTO.getTrackingNumber());
        if (StrUtil.isNotBlank(trimTracking)) {
            String logisticsCarrier = orderLogisticsInfo.getLogisticsCarrierCode();
            String logisticsCarrierCode = orderLogisticsInfo.getLogisticsCarrierCode();
            if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "FED")) {
                logisticsCarrier = "FedEx";
            } else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "UPS")) {
                logisticsCarrier = "UPS";
            } else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "AMZL")) {
                logisticsCarrier = "AMZL";
            }else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "LTL")) {
                logisticsCarrier = "LTL";
            }else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "ONTRAC")) {
                logisticsCarrier = "OnTrac";
            } else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "USPS")) {
                logisticsCarrier = "UPS";
            }
            OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();
            trackingRecord.setSku(orderItemProductSku.getSku());
            trackingRecord.setProductSkuCode(orderItemProductSku.getProductSkuCode());
            trackingRecord.setLogisticsCarrier(logisticsCarrier);
            trackingRecord.setLogisticsTrackingNo(trimTracking);
            trackingRecord.setOrderNo(orders.getOrderNo());
            trackingRecord.setOrderItemNo(orderItem.getOrderItemNo());
            trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
            // 用的bizArk仓库ID 此处轮询处理  item.getTotalQuantity()
            // 通过product_sku_code查询仓库product_sku_stock logistics_warehouse_relation warehouse 并且可用库存要大于实际需要扣减的库存
            // tag lty 库存判断
            ProductSkuStock stock = productSkuStockService.getStockForDeliver(orderAddressInfo.getCountryCode(),orderItemProductSku.getProductSkuCode(), orders.getLogisticsType(),orderItem.getTotalQuantity(),Boolean.FALSE, orderLogisticsInfo.getZipCode(),orderLogisticsInfo.getLogisticsCarrierCode(), orders.getTenantId(), orderItem.getSupplierTenantId());
            if(ObjectUtil.isNotEmpty(stock)){
                trackingRecord.setWarehouseSystemCode(stock.getWarehouseSystemCode());
            }else{
                trackingRecord.setWarehouseSystemCode(orderItemProductSku.getWarehouseSystemCode());
            }
            // tag lty 仓库
            trackingRecord.setQuantity(orderItem.getTotalQuantity());

            trackingList.add(trackingRecord);
        }
        iOrderItemTrackingRecordService.saveBatch(trackingList);
        // 如果订单是获取面单异常，将其修改为正常状态
        if (null != orders.getExceptionCode() && orders.getExceptionCode().equals(OrderExceptionEnum.logistics_attachment_exception.getValue())){
            orders.setExceptionCode(OrderExceptionEnum.normal.getValue());
            iOrdersService.updateNoTenant(orders);
        }
        try {
            if(ZSMallSystemEventUtils.checkWalletAutoPaymentEvent(orders.getTenantId(), orders.getCurrency())) {
                orderPayChain(orders.getTenantId(), Arrays.asList(orders), true, true);
            }
        }catch (Exception e){
            log.error("订单面单上传处理方法中订单支付失败!订单:{}", orders);
        }
    }

    /**
     * 订单交易记录校验，去除已经交易成功的订单
     * @param orderList
     */
    public void orderTransactionCheck(List<Orders> orderList){
        if(CollUtil.isEmpty(orderList)){
            return;
        }
        List<Orders> newOrderList = new ArrayList<>();
        for(Orders orders : orderList){
            List<TransactionRecord> transactionRecords = iTransactionRecordService.listByOrderNoList(Arrays.asList(orders.getOrderNo()));
            if(CollUtil.isNotEmpty(transactionRecords)){
                log.error("订单:{},的交易记录:{}", orders.getOrderNo(), JSONUtil.toJsonStr(transactionRecords));
                for(TransactionRecord transactionRecord : transactionRecords){
                    if(TransactionStateEnum.Success.equals(transactionRecord.getTransactionState())){
                        newOrderList.add(orders);
                        break;
                    }
                }
            }
        }
        // 去除已经交易成功的订单
        orderList.removeAll(newOrderList);
    }

    /**
     * 订单异常状态检查
     * @param orderList
     */
    public void orderExceptionStatusCheck(List<Orders> orderList) throws OrderPayException {
        if(CollUtil.isEmpty(orderList)){
            return;
        }
        for(Orders orders : orderList){
            Integer exceptionCode = orders.getExceptionCode();
            if(null == exceptionCode){
                continue;
            }
            OrderExceptionEnum orderExceptionEnum = OrderExceptionEnum.getByName(exceptionCode);
            switch (orderExceptionEnum){
                case warehouse_mapping_exception:
                case product_mapping_exception:
                    throw new OrderPayException(OrderStatusCodeEnum.ORDER_STATE_EXCEPTION);
            }
        }
    }

    /**
     * 功能描述：erp收件人地址
     *
     * @param address 住址
     * @return {@link IntactAddressInfoVo }
     * <AUTHOR>
     * @date 2025/04/11
     */
    public IntactAddressInfoVo addressToBodyForErp(OrderAddressInfo address) {
        IntactAddressInfoVo addressInfoBody = new IntactAddressInfoVo();
        String headerLanguage;
        try {
            headerLanguage = ServletUtils.getHeaderLanguage();
        } catch (Exception e) {
            headerLanguage = LanguageType.zh_CN.name();
        }
        // todo 此处拿参数设置configKey tenant.specified.display
        String addressJsonMsg = configService.selectConfigByKey("tenant.specified.display");
        Map<String, TenantSpecifiedDisplay> mapWithStream = configService.convertJsonToMapWithStream(addressJsonMsg);
        if(CollUtil.isNotEmpty(mapWithStream)){
            String tenantId = LoginHelper.getTenantId();
            TenantSpecifiedDisplay tenantSpecifiedDisplay = mapWithStream.get(tenantId);
            String displayAddress = tenantSpecifiedDisplay.getAddress();
            String displayName = tenantSpecifiedDisplay.getName();

            if (ObjectUtil.isNotNull(address)) {
                String phoneNumber = address.getPhoneNumber();
                String recipient = address.getRecipient();
//            addressInfoBody.setPhoneNumber(phoneNumber);

                addressInfoBody.setName(displayName);

                String country = address.getCountry();
                Long countryId = null;
                String countryCode = address.getCountryCode();
                if (ObjectUtil.isNotNull(countryCode)) {
                    WorldLocation worldLocation = iWorldLocationService.queryByLocationCode(countryCode, LocationTypeEnum.Country);
                    if (worldLocation != null) {
                        JSONObject locationOtherName = worldLocation.getLocationOtherName();
                        country = locationOtherName.get(headerLanguage, String.class);
                        countryId = worldLocation.getId();
                    }
                }

                String state = address.getState();
                Long stateId = null;
                String stateCode = address.getStateCode();
                if (StrUtil.isNotBlank(stateCode)) {
                    WorldLocation worldLocation = iWorldLocationService.queryByParentIdAndLocationCode(countryId, stateCode, LocationTypeEnum.State);
                    if (worldLocation != null) {
                        JSONObject locationOtherName = worldLocation.getLocationOtherName();
                        state = locationOtherName.get(headerLanguage, String.class);
                        stateId = worldLocation.getId();
                    }
                }

                addressInfoBody.setCountryId(countryId);
                addressInfoBody.setCountry(country);
                addressInfoBody.setCountryCode(countryCode);
                addressInfoBody.setStateId(stateId);
                addressInfoBody.setStateCode(stateCode);
                addressInfoBody.setState(state);
                addressInfoBody.setCity(address.getCity());
                addressInfoBody.setAddress1(address.getAddress1());
                addressInfoBody.setAddress2(address.getAddress2());
//            addressInfoBody.setZip(address.getZipCode());
                addressInfoBody.setContactInformation(phoneNumber);
            }

            List<String> addressList = new LinkedList<>();

            addressList.add(displayAddress);

            CollUtil.removeNull(addressList);
            String intactAddress = CollUtil.join(addressList, ", ");
            addressInfoBody.setIntactAddress(intactAddress);
        }
        // 提取name字段值
        return addressInfoBody;
    }


}
