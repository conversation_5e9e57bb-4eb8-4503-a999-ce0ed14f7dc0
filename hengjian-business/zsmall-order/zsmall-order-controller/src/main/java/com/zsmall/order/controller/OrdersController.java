package com.zsmall.order.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.common.web.core.BaseController;
import com.hengjian.system.mapper.SysUserMapper;
import com.hengjian.system.service.ISysConfigService;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.exception.OrderPayException;
import com.zsmall.common.exception.ProductException;
import com.zsmall.common.exception.StockException;
import com.zsmall.lottery.support.PriceBussinessV2Support;
import com.zsmall.order.biz.factory.ThirdOrderOperationFactory;
import com.zsmall.order.biz.handler.impl.TemuOrderOperationHandler;
import com.zsmall.order.biz.service.CompensationJobService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.order.*;
import com.zsmall.order.entity.domain.dto.AmazonVCOrderLogisticsAttachmentCallBackDTO;
import com.zsmall.order.entity.domain.mq.EsOrdersDTO;
import com.zsmall.order.entity.domain.vo.OrderListVo;
import com.zsmall.order.entity.domain.vo.order.OrderCurrencyAmountVO;
import com.zsmall.order.entity.domain.vo.order.OrderDetailVo;
import com.zsmall.order.entity.domain.vo.order.OrderPageVo;
import com.zsmall.order.entity.esmapper.EsOrderMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 主订单
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/business/orders")
public class OrdersController extends BaseController {

    private final OrdersService ordersService;

    private final TemuOrderOperationHandler temuOrderOperationHandler;


    private final CompensationJobService compensationJobService;
    private final ThirdOrderOperationFactory factory;

    private final OrderSupport orderSupport;
    private final PriceBussinessV2Support priceBussinessSupportV2;

    private final EsOrderMapper esOrderMapper;
    private final SysUserMapper sysUserMapper;
    private final ISysConfigService sysConfigService;


    /**
     * 查询主订单列表
     */
    @PostMapping("/list")
    public Object list(@RequestBody OrdersPageBo dto, @RequestBody PageQuery pageQuery) {
        if (Objects.isNull(dto.getIsShowOrder())){
            throw new RuntimeException("isShowOrder 是否是展示订单不能为空");
        }
        Boolean isOrderEsSearch = TenantHelper.ignore(()->Boolean.parseBoolean(sysConfigService.selectConfigByKey("Is_Order_Es_Search")));
        log.info("是否es查询:{}",isOrderEsSearch);
        dto.setIsOrderEsSearch(isOrderEsSearch);
        if (isOrderEsSearch){
          // 如果分页条数超过50000条数据，给出友好提示
          if (pageQuery.getPageNum()*pageQuery.getPageSize()>=50000) {
            return R.fail("请选择页面查询条件进行筛选数据");
          }
            dto.setIsAssignmentSort(Boolean.TRUE);
            LambdaEsQueryWrapper<EsOrdersDTO> q = ordersService.getEsOrdersQueryWrapper(dto);
            EsPageInfo<EsOrdersDTO> pageInfo = esOrderMapper.pageQuery(q,pageQuery.getPageNum(),pageQuery.getPageSize());
            List<OrderListVo> list=ordersService.dealOrderEsSearchListData(pageInfo.getList());
            return  TableDataInfo.build(list, pageInfo.getTotal());
        }else {
            return ordersService.queryPageList(dto, pageQuery);
        }

    }

    /**
     * 获取主订单详细信息
     *
     * @param orderID 订单编号
     */
    @GetMapping("/{orderID}")
    public R<OrderDetailVo> getInfo(@NotNull(message = "订单编号不能为空")
                                    @PathVariable String orderID) {
        return R.ok(ordersService.queryByOrderNo(orderID));
    }

    /**
     * 订单支付
     */
    @PostMapping("/payOrder")
    @Log(title = "订单支付", businessType = BusinessType.OTHER)
    public R<Void> payOrder(@RequestBody OrderPayBo bo) throws Exception {
        return ordersService.payOrderQueue(bo);
    }

    /**
     * 订单支付
     */
    @PostMapping("/payOrderTemp")
    @Log(title = "订单支付", businessType = BusinessType.OTHER)
    public R<Void> payOrderNoPermission(@RequestBody OrderPayBo bo) throws Exception {
        return ordersService.payOrderQueueNoPermission(bo);
    }

    /**
     * 订单支付前获取价格
     */
    @PostMapping("/payOrderBefore")
    @Log(title = "订单支付前获取价格", businessType = BusinessType.OTHER)
    public R<List<OrderCurrencyAmountVO>> payOrderBefore(@RequestBody List<String> orderNo) throws Exception {
        if(CollUtil.isEmpty(orderNo)){
            return R.fail(ZSMallStatusCodeEnum.OPERATION_CANNOT_BE_PERFORMED);
        }
        return R.ok(priceBussinessSupportV2.payOrderBeforeByCurrency(orderNo));
    }

    /**
     * 重新创建WMS订单
     * @param bo
     * @return
     */
    @PostMapping("/recreateWMSOrder")
    public R<Void> recreateWMSOrder(@RequestBody OrderNoBo bo) {
        return ordersService.recreateWMSOrder(bo);
    }

    /**
     * 获取订单列表
     * @param bo
     * @return
     */
    @GetMapping("/getOrders")
    public R<List<OrderPageVo>> getOrders(OrderNosBo bo) {
        return R.ok(ordersService.getOrders(bo));
    }

    /**
     * 功能描述：订单详情导出
     *
     * @param dto
     * @param response 响应
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/01/22
     */
    @Log(title = "订单详情导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<Void> export(@RequestBody OrdersPageBo dto, HttpServletResponse response, @RequestBody PageQuery pageQuery) {
        return ordersService.export(dto, response,pageQuery );
    }

    /**
     * 功能描述：订单详情导出2
     * @param ordersPageBo
     * @param response
     * @return
     */
    @Log(title = "订单详情导出-支持全部导出", businessType = BusinessType.EXPORT)
    @PostMapping("/exportNew")
    public R<Void> exportNew(@RequestBody OrdersPageBo ordersPageBo, HttpServletResponse response) {
        if (Objects.isNull(ordersPageBo.getIsShowOrder())){
            return R.fail("错误的请求,订单导出类型是否是展示订单不能为空");
        }
        Boolean isOrderEsSearch = TenantHelper.ignore(()->Boolean.parseBoolean(sysConfigService.selectConfigByKey("Is_Order_Es_Search")));
        ordersPageBo.setIsOrderEsSearch(isOrderEsSearch);
        if (isOrderEsSearch){
            ordersPageBo.setIsAssignmentSort(Boolean.FALSE);
            return ordersService.exportNewByEs(ordersPageBo, response );
        }else {
        return ordersService.exportNew(ordersPageBo, response );
    }

    }
    @Log(title = "订单附件导出-支持全部导出", businessType = BusinessType.EXPORT)
    @PostMapping("/exportOrderAttachment")
    public R<Void> exportOrderAttachment(@RequestBody OrdersPageBo dto, HttpServletResponse response) {
        if (ObjectUtil.notEqual(LoginHelper.getTenantType(), TenantType.Supplier.name())){
            return R.fail("订单附件导出只支持供应商");
        }
        Boolean isOrderEsSearch = TenantHelper.ignore(()->Boolean.parseBoolean(sysConfigService.selectConfigByKey("Is_Order_Es_Search")));
        dto.setIsOrderEsSearch(isOrderEsSearch);
        if (isOrderEsSearch){
            dto.setIsAssignmentSort(Boolean.FALSE);
            ordersService.exportOrderAttachmentByEs(dto, response);
        }else {
        ordersService.exportOrderAttachment(dto, response );
        }
        return R.ok();
    }

    @PostMapping("test")
    public void test (String msg) throws Exception {
        factory.getInvokeStrategy("temuOrderOperationHandler").formalTripartiteEntryForTemu(msg, new Orders());
    }

    @PostMapping("testOrderCompletion")
    public void testOrderCompletion(@RequestParam("channelSkuList") List<String> channelSkuList) throws OrderPayException, StockException, ProductException {
        orderSupport.orderCompletionProductMappingException(channelSkuList);
    }

    @PostMapping("orderCompletion")
    public void orderCompletion(@RequestParam("orderNoList")List<String> orderNoList) {
        orderSupport.orderCompletion(orderNoList);
    }

    /**
     * 标记为展示订单
     * @param ordersNos 订单号集合
     */
    @PostMapping("batchTaggingOrders")
    public R batchTaggingOrders(@RequestBody List<String> ordersNos){
        try {
            ordersService.batchTaggingOrders(ordersNos);
            return R.ok("操作成功");
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    /**
     * 还原展示订单(多个ID用,分割传递)
     * @param map 订单编号
     */
    @PostMapping("restoreOrders")
    public R restoreOrders(@RequestBody Map<String,String> map){
        try {
            String[] ids = map.get("id").split(",");
            ordersService.restoreOrders(Arrays.asList(ids));
            return R.ok("操作成功");
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /**
     * 自提/支付失败的订单 修改仓库
     */
    @PostMapping("updateOrderWarehouseCode/{orderNo}/{warehouseSystemCode}")
    public R updateOrderWarehouseCode(@PathVariable String orderNo,@PathVariable String warehouseSystemCode){
        try {
            if(ObjectUtil.equals(LoginHelper.getTenantType(),TenantType.Distributor.name())||ObjectUtil.equals(LoginHelper.getTenantType(),TenantType.Manager.name())){
                TenantHelper.ignore(()->ordersService.updateOrderWarehouseCode(orderNo,warehouseSystemCode));
            }else {
                throw new RuntimeException("非分销商不支持此操作:"+orderNo);
            }
            return R.ok("操作成功");
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据订单号获取物流面单附件
     * @param orderNoList
     * @return
     */
    @PostMapping("getOrderLogisticsAttachment")
    public R<Void> getOrderLogisticsAttachment(@RequestParam("orderNoList") List<String> orderNoList){
        ordersService.getOrderLogisticsAttachment(orderNoList);
        return R.ok();
    }

    /**
     * 根据amazonVC订单的面单数据上传面单
     * @param amazonVCOrderLogisticsAttachmentCallBackDTO
     * @return
     */
    @PostMapping("orderLogisticsAttachmentDeal")
    public R<Void> orderLogisticsAttachmentDeal(@RequestBody AmazonVCOrderLogisticsAttachmentCallBackDTO amazonVCOrderLogisticsAttachmentCallBackDTO){
        ordersService.orderLogisticsAttachmentDeal(amazonVCOrderLogisticsAttachmentCallBackDTO);
        return R.ok();
    }

    /**
     * 设置订单tracking信息
     *
     * @param bo
     * @return
     */
    @PostMapping("setOrderTrackingInfo")
    public R<Void> setOrderTrackingInfo(@RequestBody SetOrderTrackingInfoBo bo){
        ordersService.setOrderTrackingInfo(bo);
        return R.ok();
    }

    /**
     * 取消订单
     * @param orderNo
     * @return
     */
    @PostMapping("cancelOrder/{orderNo}")
    public R<Void> cancelOrder(@PathVariable String orderNo){
        try{
            if (ObjectUtil.notEqual(LoginHelper.getTenantType(), TenantType.Distributor.name())){
                return R.fail("取消单功能只支持分销商");
            }
            ordersService.cancelOrder(orderNo);
            return R.ok();
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /**
     * 取消单审核
     * @param orderNo
     * @return
     */
    @PostMapping("updateOrderCancelStatus/{orderNo}/{cancelStatus}")
    public R<Void> updateOrderCancelStatus(@PathVariable String orderNo,@PathVariable Integer cancelStatus){
        try{
            if (ObjectUtil.notEqual(LoginHelper.getTenantType(), TenantType.Supplier.name())){
                return R.fail("取消单审核功能只支持供应商");
            }
            ordersService.updateOrderCancelStatus(orderNo,cancelStatus);
            return R.ok();
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    /**
     * 功能描述：批量上传tracking
     *
     * @param file 文件
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2025/03/31
     */
    @PostMapping("/batchTrackingUpload")
    public R<Void> batchTrackingUpload(@RequestPart("file") MultipartFile file) throws IOException {
        ordersService.batchSetTracking(file);
        return R.ok();
    }
    /**
     *  功能描述：数据库数据清洗进ES
     * @return
     */
    @SaIgnore
    @PostMapping("/dealOrderDataByInsertEs")
    public void dealOrderEsData() {
        ordersService.dealOrderDataByInsertEs();
    }

    /**
     * 清洗mysql数据到ES,不使用线程池
     */
    @SaIgnore
    @PostMapping("/dealOrderDataByInsertEsWithoutThreads")
    public void dealOrderDataByInsertEsWithoutThreads() {
        ordersService.dealOrderDataByInsertEsWithoutThreads();
    }

    /**
     * 清洗完数据校验数量
     */
    @PostMapping("/checkEsOrderDate")
    public R checkEsOrderDate() {
        try {
            ordersService.checkEsOrderDate();
            return R.ok();
        }catch (Exception e){
            return R.fail(e.getMessage());
        }

    }

    /**
     * 清洗指定订单到ES
     */
    @PostMapping("/insertOrderDateToEsByOrderNos/{orderNos}")
    public R insertOrderDateToEsByOrderNos(@PathVariable(value = "orderNos") String orderNos) {
        try {
            ordersService.insertOrderDateToEsByOrderNos(orderNos);
            return R.ok();
        }catch (Exception e){
            return R.fail(e.getMessage());
        }

    }

    /**
     * 删除ES中的数据
     * @param orders
     */
    @PostMapping("/deleteOrderEsData")
    public void deleteOrderEsData(String orders) {
        LambdaEsQueryWrapper<EsOrdersDTO> s = new LambdaEsQueryWrapper<>();
        if (StrUtil.isNotEmpty(orders)){
            String[] split = orders.split(",");
            List<String> split1 = List.of(split);
            if (CollUtil.isNotEmpty(split1)){
                s.in(EsOrdersDTO::getOrderNo,split1);
            }
        }
        esOrderMapper.delete(s);
    }

    /**
     * 功能描述：将订单拉入erp
     *
     * @param orderNos 订单编号
     * <AUTHOR>
     * @date 2025/09/03
     */
    @PostMapping("/pullOrderToErp")
    public R<Void> pullOrderToErp(@RequestBody List<String> orderNos) {
        if(ObjectUtil.equals(LoginHelper.getTenantType(),TenantType.Manager.name())){
            return TenantHelper.ignore(()->compensationJobService.pullOrderToErp(orderNos));
        }else {
            throw new RuntimeException("仅支持管理员权限");
        }

    }
}
