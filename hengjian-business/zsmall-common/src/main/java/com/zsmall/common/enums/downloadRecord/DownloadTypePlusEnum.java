package com.zsmall.common.enums.downloadRecord;

import com.baomidou.mybatisplus.annotation.IEnum;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/4/1 17:12
 */
public enum DownloadTypePlusEnum implements IEnum<String> {

    Orders("订单数据"),
    TrackingOrders("物流跟踪数据"),
    WalletDetails("钱包明细"),
    ProductCustomExport("商品自定义导出"),
    RefundOrders("退款订单"),
    ProductZip("商品资料包"),
    Favorites("收藏夹"),
    Transactions("交易记录"),
    BulkOrSupTransactions("分销商/供货商交易记录"),
    Bill("供货商账单"),
    BillDetail("供货商账单详情"),
    ProductActivityPrice("活动资金列表"),
    ChinaSpotProduct("国内现货商品"),
    OffShelfProduct("下架商品"),
    FavoritesProduct("收藏夹商品"),
    FavoritesProductPackage("收藏夹商品资料包"),
    SysTenant("系统租户"),
    OrdersExport("订单导出"),
    tenantBill("分销商订单导出"),
    supperTenantBill("账单导出"),
    TenantFavoritesExport("收藏夹导出"),
    TenantSalesChannelExport("销售渠道导出"),
    ProductListExport("商品列表导出"),       // 原注释缺失，补充描述
    RulePriceExport("规则价格导出"),         // 原注释缺失，补充描述
    ProductPriceExport("商品价格导出"),       // 原注释缺失，补充描述
    TransactionsExport("交易列表导出"),
    ProductMappingExport("商品映射导出"),
    BillTransactionReceiptExport("钱包总览导出"),
    ManageInventoryExport("管理库存导出"),
    TransactionOverviewExport("交易总览明细导出"),
    OrderAttachmentExport("订单附件导出"),     // 原注释重复，修正描述
    OrderRefundExport("退款单导出"),
    TransactionsReceiptExport("交易回执单导出"),
    RechargeRecordExport("充值记录导出"),
    OrderItemTrackingExport("订单列表tracking导出"),
    OrderItemTrackingImport("订单列表tracking导入"),
    WarehouseAdminExport("超管仓库导出"),
    WarehouseSupplierExport("供应商仓库导出"),
    AdminWarehouseImport("仓库管理导入"),
    SupplierProductActivityListExport("供应商锁货活动导出"),
    SupplierProductActivityWarehouseExport("供应商锁货仓库导出"),
    SupplierProductActivityDetailsExport("供应商锁货详情导出"),
    AdminProductActivityListExport("Admin锁货活动导出"),
    AdminProductActivityWarehouseExport("Admin锁货仓库导出"),
    AdminProductActivityDetailsExport("Admin锁货详情导出"),
    DistributorProductActivityListExport("分销商锁货活动导出"),
    DistributorProductActivityDetailsExport("分销商锁货详情导出"),
    SupplierProductActivityListImport("供应商锁货活动导入"),
    ReviewRecordManagerExport("租户入驻审核"),
    StorageFeeExport("仓储费导出")
    ;

    private final String description;
    private static final Map<String, String> DESCRIPTION_MAP = Arrays.stream(values())
                                                                     .collect(Collectors.toMap(DownloadTypePlusEnum::name, DownloadTypePlusEnum::getDescription));

    DownloadTypePlusEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return this.description;
    }

    @Override
    public String getValue() {
        return this.name();
    }

    public static String getDescriptionByValue(String value) {
        return DESCRIPTION_MAP.getOrDefault(value, "未知类型");
    }

    // 可选：静态初始化校验（确保所有枚举项都有描述）
    static {
        Arrays.stream(values()).forEach(e -> {
            if (e.description == null || e.description.isEmpty()) {
                throw new IllegalStateException("枚举项 " + e.name() + " 的描述不能为空");
            }
        });
    }
}
