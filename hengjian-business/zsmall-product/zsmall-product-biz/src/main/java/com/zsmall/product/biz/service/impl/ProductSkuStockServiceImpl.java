package com.zsmall.product.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.RStatusCodeBase;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.calculate.entity.support.DeliveryFeeSupport;
import com.zsmall.calculate.entity.util.DeliveryFeeV2Utils;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.domain.DeliveryFeeByErpRequest;
import com.zsmall.common.domain.DeliveryFeeByErpResponse;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.delivery.DeliveryFeeStock;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.common.domain.dto.SaleOrderDetailDTO;
import com.zsmall.common.domain.dto.SaleOrderItemDTO;
import com.zsmall.common.domain.dto.stock.AdjustStockDTO;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokDistrictInfo;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.ExcelMessageEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.common.enums.productSku.DropShippingStockAvailableEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.common.exception.ExcelMessageException;
import com.zsmall.common.exception.StockException;
import com.zsmall.common.util.ExcelMsgBuilder;
import com.zsmall.common.util.ZExcelUtil;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import com.zsmall.order.entity.domain.OrderLogisticsInfo;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrderLogisticsInfoService;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.order.entity.mapper.OrderItemTrackingRecordMapper;
import com.zsmall.product.biz.service.ProductSkuService;
import com.zsmall.product.biz.service.ProductSkuStockService;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.domain.bo.productSkuStock.StockEditBo;
import com.zsmall.product.entity.domain.bo.productSkuStock.StockListBo;
import com.zsmall.product.entity.domain.bo.productSkuStock.StockStateSwitchBo;
import com.zsmall.product.entity.domain.dto.stock.StockUploadDTO;
import com.zsmall.product.entity.domain.export.SkuStockInfoExportDTO;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.domain.vo.ProductSkuStockVo;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAdjustStockVo;
import com.zsmall.product.entity.domain.vo.productSkuStock.SkuStockInfoVo;
import com.zsmall.product.entity.domain.vo.productSkuStock.StockValidWarehouseSelectVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.product.entity.mapper.ProductSkuStockMapper;
import com.zsmall.product.entity.util.ProductCodeGenerator;
import com.zsmall.system.entity.domain.ConfZip;
import com.zsmall.system.entity.domain.WorldLocation;
import com.zsmall.system.entity.iservice.IConfZipService;
import com.zsmall.system.entity.iservice.IWorldLocationService;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import com.zsmall.warehouse.entity.domain.LogisticsRateCountryRelation;
import com.zsmall.warehouse.entity.domain.LogisticsTemplate;
import com.zsmall.warehouse.entity.domain.LogisticsTemplateRateRule;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.ILogisticsRateCountryRelationService;
import com.zsmall.warehouse.entity.iservice.ILogisticsTemplateRateRuleService;
import com.zsmall.warehouse.entity.iservice.ILogisticsTemplateService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 商品SKU库存Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductSkuStockServiceImpl implements ProductSkuStockService {
    private final ISysTenantService sysTenantService;
    private final DeliveryFeeSupport deliveryFeeSupport;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final ProductSkuStockMapper productSkuStockMapper;
    private final IProductSkuWholesalePriceService iProductSkuWholesalePriceService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IWorldLocationService iWorldLocationService;
    private final IWarehouseService iWarehouseService;
    private final ILogisticsTemplateService iLogisticsTemplateService;
    private final ILogisticsTemplateRateRuleService iLogisticsTemplateRateRuleService;
    private final ILogisticsRateCountryRelationService iLogisticsRateCountryRelationService;
    private final ProductSupport productSupport;
    private final OrderItemTrackingRecordMapper orderItemTrackingRecordMapper;
    private final ProductCodeGenerator productCodeGenerator;
    private final ProductSkuService productSkuService;
    private final IConfZipService iConfZipService;
    private final DeliveryFeeV2Utils deliveryFeeUtils;
    private final PriceSupportV2 priceSupportV2;
    private final IOrdersService ordersService;
    private final IOrderLogisticsInfoService orderLogisticsInfoService;
    @Value("${distribution.specify.warehouse.id.bizArk}")
    public String warehouseSystemCode;


    @Override
    public void adjustStock(List<AdjustStockDTO> dtoList) throws StockException {
        for (AdjustStockDTO dto : dtoList) {
            this.adjustStock(dto, null);
        }
    }

    @Override
    public String adjustStock(AdjustStockDTO dto, String orderNo) throws StockException {
        log.info("准备开始调整库存 dto = {}", JSONUtil.toJsonStr(dto));
        String productSkuCode = dto.getProductSkuCode();

        if (StrUtil.isBlank(productSkuCode)) {
            throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXIST);
        }

        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_PRODUCT_SKU_STOCK_LOCK + productSkuCode;
        RLock lock = client.getLock(key);

        try {
            lock.lock(10L, TimeUnit.SECONDS);
            log.info("准备开始调整库存[{}]加锁成功！", key);
            String warehouseSystemCode = lockAdjustStock(dto, orderNo);

            // 库存调整成功，创建同步任务
            productSupport.createSyncTask(productSkuCode);
            return warehouseSystemCode;
        } catch (StockException e) {
            throw e;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            log.info("准备开始调整库存[{}]解锁成功！", key);
        }
    }

    @Override
    @InMethodLog(value = "查询Sku库存列表")
    public TableDataInfo<SkuStockInfoVo> queryProductSkuStockList(StockListBo bo, PageQuery pageQuery) {
        String sortValue = bo.getSort();
        Integer pageNum = pageQuery.getPageNum();
        Integer pageSize = pageQuery.getPageSize();
        Page<SkuStockInfoVo> page = new Page<>(pageNum, pageSize);
        if (StringUtils.isNotBlank(sortValue)) {
            if (sortValue.contains("Asc")) {
                page.addOrder(OrderItem.asc(StrUtil.removeSuffix(sortValue, "Asc")));
            } else {
                page.addOrder(OrderItem.asc(StrUtil.removeSuffix(sortValue, "Desc")));
            }
        } else {
            page.addOrder(OrderItem.descs("pss.update_time", "sku.sku", "p.product_code"));
        }

        IPage<SkuStockInfoVo> stockPage =TenantHelper.ignore(()->iProductSkuStockService.queryPageList(bo, page));
        List<SkuStockInfoVo> records = stockPage.getRecords();
        log.info("queryProductSkuStockList - records.size === {}", CollUtil.size(records));
        Set<String> productSkuCodeSet = records.stream()
                                                        .map(SkuStockInfoVo::getProductSkuCode) // 假设SkuStockInfoVo有一个getProductSkuCode方法
                                                        .collect(Collectors.toSet());
        Map<String, ProductSkuAttachmentVo> attachmentMap=new HashMap<>();
        if (CollUtil.isNotEmpty(productSkuCodeSet)){
            List<ProductSkuAttachmentVo> productSkuAttachmentVos = TenantHelper.ignore(()->iProductSkuAttachmentService.getBaseMapper()
                                                                                                                       .queryFirstImageByProductSkuCodes(new ArrayList<>(productSkuCodeSet)) );

            attachmentMap = productSkuAttachmentVos.stream().collect(Collectors.toMap(
                                                                                           ProductSkuAttachmentVo::getAttachmentOriginalName, // key映射函数
                                                                                           attachmentVo -> attachmentVo, // value映射函数
                                                                                           (existing, replacement) -> existing // 如果有重复的attachmentOriginalName，保留现有的
                                                                                       ));
        }


        for (SkuStockInfoVo infoVo : records) {
            String productType = infoVo.getProductType();
            String productSkuCode = infoVo.getProductSkuCode();
            ProductSkuAttachmentVo productSkuAttachmentVo = attachmentMap.get(productSkuCode);
            if (ObjectUtil.isNotNull(productSkuAttachmentVo)){
                infoVo.setImageShowUrl(productSkuAttachmentVo.getAttachmentShowUrl());
            }
//            ProductSkuAttachmentVo skuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductSkuCode(productSkuCode);
//            if (skuAttachmentVo != null) {
//                infoVo.setImageShowUrl(skuAttachmentVo.getAttachmentShowUrl());
//            }

//            if (ProductTypeEnum.WholesaleProduct.name().equals(productType)) {
//                ProductSkuWholesalePrice wholesalePrice = iProductSkuWholesalePriceService.getOneByProductIdAndProductSkuId(infoVo.getProductId(), infoVo.getProductSkuId());
//                infoVo.setUnitPrice(wholesalePrice.getOriginUnitPrice());
//            } else {
//                ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCode(infoVo.getProductSkuCode());
//                infoVo.setPickUpPrice(productSkuPrice.getOriginalPickUpPrice());
//                infoVo.setDropShippingPrice(productSkuPrice.getOriginalDropShippingPrice());
//            }

            if (WarehouseTypeEnum.BizArk.name().equals(infoVo.getWarehouseType())) {
                infoVo.setCanModify(false);
            }
            // 设置一件代发库存
            if(null != infoVo.getDropShippingStockAvailable() && infoVo.getDropShippingStockAvailable() == DropShippingStockAvailableEnum.NOT_SINGLE.getCode()){
                infoVo.setProxyStockTotal(infoVo.getStockTotal());
            }else {
                infoVo.setProxyStockTotal(0);
            }

        }
        return TableDataInfo.build(records, stockPage.getTotal());
    }

    /**
     * 编辑库存数量
     *
     * @param bo
     */
    @Override
    public R<Void> editStockQuantity(StockEditBo bo) {
        LoginHelper.getLoginUser(TenantType.Supplier);

        Integer stockTotal = bo.getStockTotal();
        String stockCode = bo.getStockCode();
        String warehouseSystemCode = bo.getWarehouseSystemCode();

        ProductSkuStock productSkuStock = iProductSkuStockService.queryByStockCode(stockCode);
        if (productSkuStock != null) {
            String nowWarehouseSystemCode = productSkuStock.getWarehouseSystemCode();
            String productSkuCode = productSkuStock.getProductSkuCode();

            RedissonClient client = RedisUtils.getClient();
            String key = RedisConstants.ZSMALL_PRODUCT_SKU_STOCK_LOCK + productSkuCode;
            RLock lock = client.getLock(key);

            try {
                lock.lock(10L, TimeUnit.SECONDS);
                log.info("编辑库存数量[{}]加锁成功！", key);

                Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCode(nowWarehouseSystemCode);

                WarehouseTypeEnum warehouseType = warehouse.getWarehouseType();
                if (Objects.equals(warehouseType, WarehouseTypeEnum.BizArk)) {
                    return R.fail(ZSMallStatusCodeEnum.BIZARK_WAREHOUSE_CANNOT_EDIT_STOCK);
                }

                if (StringUtils.equals(warehouseSystemCode, nowWarehouseSystemCode)) {
                    // 是同一个仓库，直接设置库存数量
                    productSkuStock.setStockTotal(stockTotal);
                    productSkuStock.setStockAvailable(stockTotal);
                    iProductSkuStockService.updateById(productSkuStock);
                } else {
                    Warehouse newWarehouse = iWarehouseService.queryByWarehouseSystemCode(nowWarehouseSystemCode);

                    if (newWarehouse != null) {
                        // 不是同一个仓库，需要把当前库存删除，在新的仓库创建库存
                        ProductSkuStock newStock = new ProductSkuStock();
                        newStock.setStockCode(productCodeGenerator.codeGenerate(BusinessCodeEnum.StockCode));
                        newStock.setStockTotal(stockTotal);
                        newStock.setStockReserved(0);
                        newStock.setStockAvailable(stockTotal);
                        newStock.setStockState(GlobalStateEnum.Valid);
                        newStock.setErpSku(productSkuStock.getErpSku());
                        newStock.setProductCode(productSkuStock.getProductCode());
                        newStock.setProductSkuCode(productSkuCode);
                        newStock.setWarehouseSystemCode(warehouseSystemCode);
                        iProductSkuStockService.save(newStock);
                        iProductSkuStockService.removeById(productSkuStock);
                    } else {
                        return R.fail(ZSMallStatusCodeEnum.WAREHOUSE_NOT_EXIST);
                    }
                }

                ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);

                if (productSku != null) {
                    productSkuService.setProductSkuStock(CollUtil.newArrayList(productSku));

                    // 创建库存同步任务
                    productSupport.createSyncTask(productSku.getId());
                    iProductSkuService.updateById(productSku);
                }
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
                log.info("编辑库存数量[{}]解锁成功！", key);
            }
        } else {
            return R.fail(ZSMallStatusCodeEnum.STOCK_STATUS_UNKNOWN);
        }
        return R.ok();
    }

    /**
     * 切换库存状态
     *
     * @param bo
     */
    @Override
    public R<Void> switchStockState(StockStateSwitchBo bo) {
        LoginHelper.getLoginUser(TenantType.Supplier);

        String stockCode = bo.getStockCode();
        Integer stockState = bo.getStockState();

        ProductSkuStock productSkuStock = iProductSkuStockService.queryByStockCode(stockCode);
        if (productSkuStock == null) {
            return R.fail(ZSMallStatusCodeEnum.STOCK_STATUS_UNKNOWN);
        }
        String productSkuCode = productSkuStock.getProductSkuCode();

        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_PRODUCT_SKU_STOCK_LOCK + productSkuCode;
        RLock lock = client.getLock(key);

        try {
            lock.lock(10L, TimeUnit.SECONDS);
            log.info("切换库存状态[{}]加锁成功！", key);

            GlobalStateEnum globalStateEnum = GlobalStateEnum.fromCode(stockState);
            productSkuStock.setStockState(globalStateEnum);
            iProductSkuStockService.updateById(productSkuStock);

            ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
            if (productSku != null) {
                productSkuService.setProductSkuStock(CollUtil.newArrayList(productSku));

                // 创建库存同步任务
                productSupport.createSyncTask(productSku.getId());
                iProductSkuService.updateById(productSku);
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            log.info("切换库存状态[{}]解锁成功！", key);
        }
        return R.ok();
    }

    /**
     * 查询库存可用仓库
     *
     * @param stockCode
     */
    @Override
    public R<List<StockValidWarehouseSelectVo>> queryValidWarehouseList(String stockCode) {
        LoginHelper.getLoginUser(TenantType.Supplier);

        ProductSkuStock productSkuStock = iProductSkuStockService.queryByStockCode(stockCode);
        if (productSkuStock == null) {
            return R.fail(ZSMallStatusCodeEnum.STOCK_STATUS_UNKNOWN);
        }

        String productSkuCode = productSkuStock.getProductSkuCode();
        String nowWarehouseSystemCode = productSkuStock.getWarehouseSystemCode();
        List<ProductSkuStockVo> stockVoList = iProductSkuStockService.queryByProductSkuCode(productSkuCode);
        List<String> existWarehouse = stockVoList.stream().map(ProductSkuStockVo::getWarehouseSystemCode)
                                                 .collect(Collectors.toList());

        List<StockValidWarehouseSelectVo> voList = new ArrayList<>();
        List<Warehouse> warehouseList = iWarehouseService.queryByWarehouseType(WarehouseTypeEnum.OwnWarehouse);
        for (Warehouse warehouse : warehouseList) {
            String warehouseName = warehouse.getWarehouseName();
            String warehouseSystemCode = warehouse.getWarehouseSystemCode();

            StockValidWarehouseSelectVo vo = new StockValidWarehouseSelectVo();
            vo.setWarehouseName(warehouseName);
            vo.setWarehouseSystemCode(warehouseSystemCode);

            if (!StrUtil.equals(nowWarehouseSystemCode, warehouseSystemCode) && existWarehouse.contains(warehouseSystemCode)) {
                // 此仓库也存有该SKU的库存，禁用选项
                vo.setDisabled(true);
            } else {
                vo.setDisabled(false);
            }
            voList.add(vo);
        }
        return R.ok(voList);
    }

    /**
     * 上传库存Excel
     *
     * @param file
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<Void> uploadStockExcel(MultipartFile file) throws Exception {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);

        if (file == null) {
            return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
        }

        InputStream inputStream = file.getInputStream();
        ExcelReader reader = ExcelUtil.getReader(inputStream);

        if (reader != null) {
            Sheet sheet = reader.getSheet();
            String sheetName = sheet.getSheetName();
            log.info("上传库存Excel - sheetName = {}", sheetName);
            int columnCount = reader.getColumnCount();
            log.info("上传库存Excel - columnCount = {}", columnCount);
            if (columnCount != 4) {
                return R.fail(ZSMallStatusCodeEnum.EXCEL_COLUMN_COUNT_NOT_MATCH);
            }

            try {
                ExcelMsgBuilder<StockUploadDTO> builder = ZExcelUtil.msgBuilder(reader, 0, StockUploadDTO.class);
                builder.setMsgPrefix("<p>").setMsgSuffix("</p></br>");

                List<StockUploadDTO> dtoList = ZExcelUtil.parseFieldDTO(reader, StockUploadDTO.class, 0, 1);
                if (CollUtil.isNotEmpty(dtoList)) {
                    List<String> productSkuCodeList = dtoList.stream().map(StockUploadDTO::getProductSkuCode).distinct().collect(Collectors.toList());
                    Map<String, ProductSkuStock> productSkuStockMap = new HashMap<>();
                    LocaleMessage globalMessage = new LocaleMessage();
                    for (StockUploadDTO stockUploadDTO : dtoList) {
                        builder.setNowShowRow(stockUploadDTO.getShowRowIndex());
                        String productSkuCode = stockUploadDTO.getProductSkuCode();
                        String warehouseSystemCode = stockUploadDTO.getWarehouseSystemCode();
                        Integer quantity = stockUploadDTO.getQuantity();
                        String dropShippingQuantity = stockUploadDTO.getDropShippingQuantity();
                        String mapKey = productSkuCode + "-" + warehouseSystemCode;

                        if (quantity < 0) {
                            globalMessage.append(builder.build(StockUploadDTO::getQuantity, ExcelMessageEnum.ILLEGAL_ARGUMENT));
                            continue;
                        }

                        if(StringUtils.isEmpty(dropShippingQuantity)){
                            globalMessage.append(builder.build(StockUploadDTO::getDropShippingQuantity, ExcelMessageEnum.ILLEGAL_ARGUMENT));
                            continue;
                        }

                        if(!dropShippingQuantity.equals("0") && !dropShippingQuantity.equals("等于自提库存")){
                            globalMessage.append(builder.build(StockUploadDTO::getDropShippingQuantity, ExcelMessageEnum.ILLEGAL_DROP_SHIPPING_QUANTITY));
                            continue;
                        }

                        ProductSkuStock productSkuStock = productSkuStockMap.get(mapKey);
                        if (productSkuStock == null) {
                            productSkuStock = iProductSkuStockService.queryByProductSkuCode(productSkuCode, warehouseSystemCode);
                        }

                        if (productSkuStock != null) {
                            productSkuStock.setStockTotal(quantity);
                            productSkuStock.setStockAvailable(quantity);
                            if(dropShippingQuantity.equals("0")){
                                productSkuStock.setDropShippingStockAvailable(DropShippingStockAvailableEnum.SINGLE.getCode());
                            }
                            if(dropShippingQuantity.equals("等于自提库存")){
                                productSkuStock.setDropShippingStockAvailable(DropShippingStockAvailableEnum.NOT_SINGLE.getCode());
                            }
                            productSkuStockMap.put(mapKey, productSkuStock);
                        } else {
                            globalMessage.append(builder.build(StockUploadDTO::getWarehouseSystemCode, ExcelMessageEnum.PRODUCT_SKU_NOT_IN_WAREHOUSE));
                        }
                    }

                    if (globalMessage.hasData()) {
                        throw new ExcelMessageException(globalMessage);
                    } else {
                        Collection<ProductSkuStock> values = productSkuStockMap.values();
                        iProductSkuStockService.updateBatchById(values);

                        List<ProductSku> productSkuList = iProductSkuService.queryByProductSkuCodeList(productSkuCodeList);
                        productSkuService.setProductSkuStock(productSkuList);
                        iProductSkuService.updateBatchById(productSkuList);
                        List<Long> skuIdList = productSkuList.stream().map(ProductSku::getId)
                                                             .collect(Collectors.toList());
                        productSupport.createSyncTask(skuIdList);
                    }
                } else {
                    return R.fail(ZSMallStatusCodeEnum.EXCEL_NOT_EXIST_VALID_ROW);
                }
            } catch (ExcelMessageException e) {
                log.error("上传库存Excel 出现业务错误，原因：{}", e.getMessage(), e);
                return R.failHtml(e.getLocaleMessage().toMessage());
            } catch (Exception e) {
                log.error("上传库存Excel 出现未知错误，原因：{}", e.getMessage(), e);
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_EXCEL_ERROR);
            }
        } else {
            return R.fail(ZSMallStatusCodeEnum.EXCEL_NOT_EXIST_VALID_ROW);
        }
        return R.ok();
    }
    private String lockAdjustStockForErp(AdjustStockDTO dto, String orderNo) throws StockException {
        String productSkuCode = dto.getProductSkuCode();

        try {
            Integer adjustQuantity = dto.getAdjustQuantity();
            String destCountry = dto.getDestCountry();
            String destZipCode = dto.getDestZipCode();
            String specifyWarehouse = dto.getSpecifyWarehouse();

            ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productSkuCode);
            if (adjustStockVo == null) {
                throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXISTS.args(productSkuCode));
            }
            log.info("调整库存 adjustStockVo = {}", JSONUtil.toJsonStr(adjustStockVo));

            ShelfStateEnum spuShelfState = adjustStockVo.getSpuShelfState();
            ShelfStateEnum skuShelfState = adjustStockVo.getSkuShelfState();
            Integer stockTotal = adjustStockVo.getStockTotal();
            String finalWarehouseSystemCode = null;
            ProductSkuStock finalStock = null;
            if (adjustQuantity > 0) {

                if (StrUtil.isNotBlank(specifyWarehouse)) {
                    // 需要归还至指定仓库
                    finalStock = iProductSkuStockService.queryValidByProductSkuCodeNotTenant(productSkuCode, specifyWarehouse);
                }

            } else {
                // 最终选定的库存
                // 若满足，主商品不为上架、有效，SKU不为有效其中一个，则该商品库存不足
                if (!ShelfStateEnum.OnShelf.equals(spuShelfState) || !ShelfStateEnum.OnShelf.equals(skuShelfState)) {
                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXISTS.args(productSkuCode));
                }

                if (NumberUtil.compare(adjustQuantity, stockTotal) > 0) {
                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
                }

                if (StringUtils.isNotBlank(specifyWarehouse)) {  // 指定仓库发货只会判断一次库存
                    finalStock = iProductSkuStockService.queryValidByProductSkuCodeNotTenant(productSkuCode, specifyWarehouse);
//                    int absoluteAdjustQuantity = Math.abs(adjustQuantity);
//                    if(ObjectUtil.isNotEmpty(finalStock)&&finalStock.getStockAvailable()<absoluteAdjustQuantity){
//                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateInStock( productSkuCode, adjustQuantity);
//                        finalStock = stockList.get(0);
//                    }

                } else {  // 不指定仓库发货，根据物流模板绑定的仓库发货
                    // 物流方式，自提的订单，可以全世界范围内发货
                    LogisticsTypeEnum logisticsType = dto.getLogisticsType();
                    // 不指定仓库发货的,
                    if (StrUtil.equals(destCountry, "US")) {
                        String finalLogisticsTemplateNo = null;
                        Boolean logisticsAccount = dto.getLogisticsAccount();

                        List<LogisticsTemplate> templateList = new ArrayList<>();
                        // 代发需要查询是否存在物流模板关联，若存在，则需要筛选出时间最短的
//                        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                            templateList = iLogisticsTemplateService.queryAdequateStockLogisticsTemplate(productSkuCode, adjustQuantity, logisticsAccount);
//                            if (CollUtil.isNotEmpty(templateList)) {
//                                finalLogisticsTemplateNo = siftLogisticsTemplate(templateList, destZipCode);
//                            }
//                        }

                        // 根据物流模板找最近的仓库，并且要考虑到仓库是否支持第三方账号
                        // 仓库改动:有库存就行
//                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateStockByParams(destCountry, productSkuCode, logisticsType, adjustQuantity, finalLogisticsTemplateNo, logisticsAccount);
                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateInStock( productSkuCode, adjustQuantity);

                        log.info("调整库存 stockList = {}", JSONUtil.toJsonStr(stockList));
                        // tag lty 库存v1
                        // 自提的因为是全世界范围内选择库存，所以需要判断有没有当前国家的库存，若有则优先取当前国家的库存，没有则取其他国家的随机
                        if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                            List<ProductSkuStock> currentCountry = stockList.stream().filter(stock -> stock.getCountry()
                                                                                                           .equals(destCountry))
                                                                            .collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(currentCountry)) {
                                stockList = currentCountry;
                            }
                        }

                        if (CollUtil.size(stockList) == 1) {
                            finalStock = stockList.get(0);
                        } else if (CollUtil.size(stockList) > 1) {
                            finalStock = stockList.get(RandomUtil.randomInt(0, CollUtil.size(stockList)));
                        } else if (CollUtil.isNotEmpty(templateList)) {
                            throw new RStatusCodeException(ZSMallStatusCodeEnum.TARGET_CITY_NOT_SUPPORT_ERROR);
                        }
                    } else {
                        // 非美国的目的地，随机选一个非美国的仓库 产品没考虑到这块逻辑 8.5号 产品的意思是没有非美国地区的订单 有了再说
                        Boolean logisticsAccount = dto.getLogisticsAccount();
//                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateStockByParams(destCountry, productSkuCode, logisticsType, adjustQuantity, null, logisticsAccount);
                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateInStock( productSkuCode, adjustQuantity);
                        if (CollUtil.isNotEmpty(stockList)) {
                            finalStock = stockList.get(RandomUtil.randomInt(0, CollUtil.size(stockList)));
                        }
                    }
                }
            }

            // 选中了最终库存后，开始扣减
            if (finalStock != null) {
                Integer stockAvailable = finalStock.getStockAvailable();
                Integer newStockAvailable = NumberUtil.add(stockAvailable, adjustQuantity).intValue();
                log.info("调整库存 最终库存 = {}，新的可用库存数量 = {}", JSONUtil.toJsonStr(finalStock), newStockAvailable);

                if (newStockAvailable >= 0) {
                    finalWarehouseSystemCode = finalStock.getWarehouseSystemCode();
                    finalStock.setStockAvailable(newStockAvailable);
                    finalStock.setStockTotal(newStockAvailable);
                    iProductSkuStockService.updateByIdNotTenant(finalStock);
                } else if (adjustQuantity < 0) {  // 只有扣除库存时需要报错库存不足，归还库存时不需要报错
                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
                }
            } else if (adjustQuantity < 0) {  // 只有扣除库存时需要报错库存不足，归还库存时不需要报错
                throw new StockException(ZSMallStatusCodeEnum.NO_AVAILABLE_STOCK_FOUND.args(productSkuCode));
            }

            // 重新计算SKU总库存
            updateStockTotal(spuShelfState, skuShelfState, productSkuCode);
            if (ObjectUtil.isNotEmpty(orderNo)){
                LambdaQueryWrapper<OrderItemTrackingRecord> eq = new LambdaQueryWrapper<OrderItemTrackingRecord>().eq(OrderItemTrackingRecord::getOrderNo, orderNo)
                                                                                                                  .eq(OrderItemTrackingRecord::getDelFlag, 0);
                List<OrderItemTrackingRecord> orderItemTrackingRecords = orderItemTrackingRecordMapper.selectList(eq);

                String finalWarehouseSystemCode1 = finalWarehouseSystemCode;
                orderItemTrackingRecords.stream().forEach(orderItemTrackingRecordVo -> {
                    orderItemTrackingRecordVo.setWarehouseSystemCode(finalWarehouseSystemCode1);
                });
                try{
                    if (CollUtil.isNotEmpty(orderItemTrackingRecords)){
                        orderItemTrackingRecordMapper.updateBatchById(orderItemTrackingRecords);
                    }
                }catch (Exception e){
                    log.error("订单号[{}]更新仓库失败",orderNo,e);
                }
            }

            return finalWarehouseSystemCode;
        } catch (StockException e) {
            log.error("商品[{}]库存调整出现库存错误 = {}", productSkuCode, e.getMessage(), e);
            throw new StockException(ZSMallStatusCodeEnum.NO_AVAILABLE_STOCK_FOUND.args(productSkuCode));
        } catch (RStatusCodeException e) {
            log.error("商品[{}]库存调整出现状态码错误 = {}", productSkuCode, e.getMessage(), e);
            RStatusCodeBase statusCode = e.getStatusCode();
            throw new StockException(statusCode);
        } catch (Exception e) {
            log.error("商品[{}]库存调整出现未知错误 = {}", productSkuCode, e.getMessage(), e);
            throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_ADJUST_STOCK_ERROR.args(productSkuCode));
        }

    }

    private String lockAdjustStock(AdjustStockDTO dto, String orderNo) throws StockException {
        String productSkuCode = dto.getProductSkuCode();

        try {
            Integer adjustQuantity = dto.getAdjustQuantity();
            String destCountry = dto.getDestCountry();
            String destZipCode = dto.getDestZipCode();
            String specifyWarehouse = dto.getSpecifyWarehouse();

            ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productSkuCode);
            if (adjustStockVo == null) {
                throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXISTS.args(productSkuCode));
            }
            log.info("调整库存 adjustStockVo = {}", JSONUtil.toJsonStr(adjustStockVo));

            ShelfStateEnum spuShelfState = adjustStockVo.getSpuShelfState();
            ShelfStateEnum skuShelfState = adjustStockVo.getSkuShelfState();
            Integer stockTotal = adjustStockVo.getStockTotal();
            String finalWarehouseSystemCode = null;
            ProductSkuStock finalStock = null;
            if (adjustQuantity > 0) {

                if (StrUtil.isNotBlank(specifyWarehouse)) {
                    // 需要归还至指定仓库
                    finalStock = iProductSkuStockService.queryValidByProductSkuCodeNotTenant(productSkuCode, specifyWarehouse);
                }

            } else {
                // 最终选定的库存
                // 若满足，主商品不为上架、有效，SKU不为有效其中一个，则该商品库存不足
                if (!ShelfStateEnum.OnShelf.equals(spuShelfState) || !ShelfStateEnum.OnShelf.equals(skuShelfState)) {
                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXISTS.args(productSkuCode));
                }

                if (NumberUtil.compare(adjustQuantity, stockTotal) > 0) {
                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
                }
//                if (StringUtils.isNotBlank(specifyWarehouse)) {  // 指定仓库发货只会判断一次库存
//                    finalStock = iProductSkuStockService.queryValidByProductSkuCodeNotTenant(productSkuCode, specifyWarehouse);
//                    int absoluteAdjustQuantity = Math.abs(adjustQuantity);
//                    if(ObjectUtil.isNotEmpty(finalStock)&&finalStock.getStockAvailable()<absoluteAdjustQuantity){
//                        Orders byOrderNo = ordersService.getByOrderNo(orderNo);
//                        if (ObjectUtil.equals(byOrderNo.getLogisticsType(),LogisticsTypeEnum.PickUp)){
//                            throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
//                        }else {
//                            List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateInStock( productSkuCode, adjustQuantity);
//                            if(CollUtil.isNotEmpty(stockList)){
//                                finalStock = stockList.get(0);
//                            }else {
//                                throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
//                            }
//                        }
//                    }
//                }
                Orders byOrderNo = ordersService.getByOrderNo(orderNo);
                String countryCode = byOrderNo.getCountryCode();
                // 指定仓库发货只会判断一次库存
                if (StringUtils.isNotBlank(specifyWarehouse)) {
                    if (ObjectUtil.equals(byOrderNo.getLogisticsType(),LogisticsTypeEnum.PickUp)) {
                        // 仓库可配送国家判断
                        List<String> countryCodeList = TenantHelper.ignore(() -> iWarehouseService.listCountryByWarehouseSystemCode(specifyWarehouse));
                        if(CollUtil.isNotEmpty(countryCodeList)){
                            if(!countryCodeList.contains(countryCode)){
                                byOrderNo.setOrderState(OrderStateType.Failed);
                                JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE)
                                                                   .toJSON();
                                byOrderNo.setPayErrorMessage(errorMsg);
                                throw new StockException(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE);
                            }
                        }else {
                            throw new StockException(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE);
                        }
                        finalStock = iProductSkuStockService.queryValidByProductSkuCodeNotTenant(productSkuCode, specifyWarehouse);
                        int absoluteAdjustQuantity = Math.abs(adjustQuantity);
                        if (ObjectUtil.isNotEmpty(finalStock) && finalStock.getStockAvailable() < absoluteAdjustQuantity) {
                            throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
                        }
                    }
                    if (ObjectUtil.equals(byOrderNo.getLogisticsType(), LogisticsTypeEnum.DropShipping)) {
                        // 代发筛选使用代发的库存
                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateInStockAndDropShippingStockAvailable(productSkuCode, adjustQuantity, DropShippingStockAvailableEnum.NOT_SINGLE.getCode(),specifyWarehouse);
                        if (CollUtil.isNotEmpty(stockList)) {
                            List<String> warehouseSystemCodeList = stockList.stream().map(ProductSkuStock::getWarehouseSystemCode)
                                                            .collect(Collectors.toList());
                            Boolean isMatch = false;
                            for (String warehouseSystemCode : warehouseSystemCodeList) {
                                // 仓库可配送国家判断
                                List<String> countryCodeList = TenantHelper.ignore(() -> iWarehouseService.listCountryByWarehouseSystemCode(warehouseSystemCode));
                                if(CollUtil.isNotEmpty(countryCodeList)){
                                    if(countryCodeList.contains(countryCode)){
                                        isMatch = true;
                                        Stream<ProductSkuStock> productSkuStockStream = stockList.stream()
                                                                                                 .filter(entity -> ObjectUtil.equals(entity.getWarehouseSystemCode(), warehouseSystemCode));
                                        finalStock = productSkuStockStream.findFirst().get();
                                        break;
                                    }
                                }
                            }
                            // 修改订单状态和错误信息
                            if(!isMatch){
                                byOrderNo.setOrderState(OrderStateType.Failed);
                                JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE)
                                                                   .toJSON();
                                byOrderNo.setPayErrorMessage(errorMsg);
                                throw new StockException(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE);
                            }
//                            finalStock = stockList.get(0);
                        } else {
                            throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
                        }
                    }
                } else {  // 不指定仓库发货，根据物流模板绑定的仓库发货
                    // 物流方式，自提的订单，可以全世界范围内发货
                    LogisticsTypeEnum logisticsType = dto.getLogisticsType();
                    // 不指定仓库发货的,
                    if (StrUtil.equals(destCountry, "US")) {
                        String finalLogisticsTemplateNo = null;
                        Boolean logisticsAccount = dto.getLogisticsAccount();

                        List<LogisticsTemplate> templateList = new ArrayList<>();
                        // 代发需要查询是否存在物流模板关联，若存在，则需要筛选出时间最短的
//                        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                            templateList = iLogisticsTemplateService.queryAdequateStockLogisticsTemplate(productSkuCode, adjustQuantity, logisticsAccount);
//                            if (CollUtil.isNotEmpty(templateList)) {
//                                finalLogisticsTemplateNo = siftLogisticsTemplate(templateList, destZipCode);
//                            }
//                        }

                        // 根据物流模板找最近的仓库，并且要考虑到仓库是否支持第三方账号
                        // 仓库改动:有库存就行
//                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateStockByParams(destCountry, productSkuCode, logisticsType, adjustQuantity, finalLogisticsTemplateNo, logisticsAccount);
                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateInStock( productSkuCode, adjustQuantity);

                        log.info("调整库存 stockList = {}", JSONUtil.toJsonStr(stockList));
                        // tag lty 库存v1
                        // 自提的因为是全世界范围内选择库存，所以需要判断有没有当前国家的库存，若有则优先取当前国家的库存，没有则取其他国家的随机
                        if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                            List<ProductSkuStock> currentCountry = stockList.stream().filter(stock -> stock.getCountry()
                                                                                                           .equals(destCountry))
                                                                            .collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(currentCountry)) {
                                stockList = currentCountry;
                            }
                        }else {
                            // 代发筛选使用代发的库存
                            stockList = iProductSkuStockService.queryAdequateInStockAndDropShippingStockAvailable( productSkuCode, adjustQuantity,DropShippingStockAvailableEnum.NOT_SINGLE.getCode(),null);
                            if(CollUtil.isEmpty(stockList)){
                                throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
                            }
                        }


                        if (CollUtil.size(stockList) == 1) {
                            finalStock = stockList.get(0);
                        } else if (CollUtil.size(stockList) > 1) {
                            finalStock = stockList.get(RandomUtil.randomInt(0, CollUtil.size(stockList)));
                        } else if (CollUtil.isNotEmpty(templateList)) {
                            throw new RStatusCodeException(ZSMallStatusCodeEnum.TARGET_CITY_NOT_SUPPORT_ERROR);
                        }
//                        if (StrUtil.isNotBlank(specifyWarehouse)) {
//                            // 指定仓库发货的，需要判断是否有库存
//                            finalStock = iProductSkuStockService.queryValidByProductSkuCodeNotTenant(productSkuCode, specifyWarehouse);
//                        }
                    } else {
                        // 非美国的目的地，随机选一个非美国的仓库 产品没考虑到这块逻辑 8.5号 产品的意思是没有非美国地区的订单 有了再说
                        Boolean logisticsAccount = dto.getLogisticsAccount();
//                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateStockByParams(destCountry, productSkuCode, logisticsType, adjustQuantity, null, logisticsAccount);
                        List<ProductSkuStock> stockList = null;
                        if (ObjectUtil.equals(logisticsType,LogisticsTypeEnum.PickUp)) {
                           stockList = iProductSkuStockService.queryAdequateInStock( productSkuCode, adjustQuantity);
                        }else {
                            stockList = iProductSkuStockService.queryAdequateInStockAndDropShippingStockAvailable( productSkuCode, adjustQuantity,DropShippingStockAvailableEnum.NOT_SINGLE.getCode(),null);
                        }
                        // 发货仓库国家是否匹配判断
                        if (CollUtil.isNotEmpty(stockList)) {
                            List<String> warehouseSystemCodeList = stockList.stream().map(ProductSkuStock::getWarehouseSystemCode)
                                                                            .collect(Collectors.toList());
                            Boolean isMatch = false;
                            for (String warehouseSystemCode : warehouseSystemCodeList) {
                                // 仓库可配送国家判断
                                List<String> countryCodeList = TenantHelper.ignore(() -> iWarehouseService.listCountryByWarehouseSystemCode(warehouseSystemCode));
                                if(CollUtil.isNotEmpty(countryCodeList)){
                                    if(countryCodeList.contains(countryCode)){
                                        isMatch = true;
                                        Stream<ProductSkuStock> productSkuStockStream = stockList.stream()
                                                                                                 .filter(entity -> ObjectUtil.equals(entity.getWarehouseSystemCode(), warehouseSystemCode));
                                        finalStock = productSkuStockStream.findFirst().get();
                                        break;
                                    }
                                }
                            }
                            // 修改订单状态和错误信息
                            if(!isMatch){
                                byOrderNo.setOrderState(OrderStateType.Failed);
                                JSONObject errorMsg = LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE)
                                                                   .toJSON();
                                byOrderNo.setPayErrorMessage(errorMsg);
                                throw new StockException(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE);
                            }
                        }
                    }
                }
            }

            // 选中了最终库存后，开始扣减
            if (finalStock != null) {
                Integer stockAvailable = finalStock.getStockAvailable();
                Integer newStockAvailable = NumberUtil.add(stockAvailable, adjustQuantity).intValue();
                log.info("调整库存 最终库存 = {}，新的可用库存数量 = {}", JSONUtil.toJsonStr(finalStock), newStockAvailable);

                if (newStockAvailable >= 0) {
                    finalWarehouseSystemCode = finalStock.getWarehouseSystemCode();
                    finalStock.setStockAvailable(newStockAvailable);
                    finalStock.setStockTotal(newStockAvailable);
                    iProductSkuStockService.updateByIdNotTenant(finalStock);
                } else if (adjustQuantity < 0) {  // 只有扣除库存时需要报错库存不足，归还库存时不需要报错
                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
                }
            } else if (adjustQuantity < 0) {  // 只有扣除库存时需要报错库存不足，归还库存时不需要报错
                throw new StockException(ZSMallStatusCodeEnum.NO_AVAILABLE_STOCK_FOUND.args(productSkuCode));
            }

            // 重新计算SKU总库存
            updateStockTotal(spuShelfState, skuShelfState, productSkuCode);
            if (ObjectUtil.isNotEmpty(orderNo)){
                LambdaQueryWrapper<OrderItemTrackingRecord> eq = new LambdaQueryWrapper<OrderItemTrackingRecord>().eq(OrderItemTrackingRecord::getOrderNo, orderNo)
                                                                                                                  .eq(OrderItemTrackingRecord::getDelFlag, 0);
                List<OrderItemTrackingRecord> orderItemTrackingRecords = orderItemTrackingRecordMapper.selectList(eq);

                String finalWarehouseSystemCode1 = finalWarehouseSystemCode;
                orderItemTrackingRecords.stream().forEach(orderItemTrackingRecordVo -> {
                    orderItemTrackingRecordVo.setWarehouseSystemCode(finalWarehouseSystemCode1);
                });
                try{
                    if (CollUtil.isNotEmpty(orderItemTrackingRecords)){
                        orderItemTrackingRecordMapper.updateBatchById(orderItemTrackingRecords);
                    }
                }catch (Exception e){
                    log.error("订单号[{}]更新仓库失败",orderNo,e);
                }
            }

            return finalWarehouseSystemCode;
        } catch (StockException e) {
            log.error("商品[{}]库存调整出现库存错误 = {}", productSkuCode, e.getMessage(), e);
            if(LocaleMessage.byStatusCode(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE).toJSON().toString().equals(e.getLocaleMessage().toJSON().toString())){
                throw new StockException(OrderStatusCodeEnum.ORDER_CANNOT_MATCH_WAREHOUSE);
            }
            throw new StockException(ZSMallStatusCodeEnum.NO_AVAILABLE_STOCK_FOUND.args(productSkuCode));
        } catch (RStatusCodeException e) {
            log.error("商品[{}]库存调整出现状态码错误 = {}", productSkuCode, e.getMessage(), e);
            RStatusCodeBase statusCode = e.getStatusCode();
            throw new StockException(statusCode);
        } catch (Exception e) {
            log.error("商品[{}]库存调整出现未知错误 = {}", productSkuCode, e.getMessage(), e);
            throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_ADJUST_STOCK_ERROR.args(productSkuCode));
        }

    }

    /**
     * 筛选物流模板（目前规则：物流模板的最慢到达时间为最小的）
     *
     * @return
     */
    //方法弃用的注解
    @Deprecated
    private String siftLogisticsTemplate(List<LogisticsTemplate> logisticsTemplateList,
                                         String destZipCode) throws RStatusCodeException {
        // 最终选定的物流模板
        String finalLogisticsTemplate = null;
        WorldLocation worldLocation = iWorldLocationService.queryByZipCode(destZipCode);
        if (ObjectUtil.isNull(worldLocation)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.ZIP_CODE_DOES_NOT_MATCH_CITY);
        }
        Long worldLocationId = worldLocation.getId();

        // 目前最小
        Integer nowSlowestDeliveryTime = null;
        for (LogisticsTemplate logisticsTemplate : logisticsTemplateList) {
            Long logisticsTemplateId = logisticsTemplate.getId();
            String ltn = logisticsTemplate.getLogisticsTemplateNo();

            //获取关联表信息
            LogisticsRateCountryRelation relation = iLogisticsRateCountryRelationService.getByLogisticsTemplateIdAndCountryId(logisticsTemplateId, worldLocationId);
            if (ObjectUtil.isNull(relation)) {
                log.error("模板关联不存在");
                continue;
            }

            Long rateRuleId = relation.getLogisticsTemplateRateRuleId();
            LogisticsTemplateRateRule rateRule = iLogisticsTemplateRateRuleService.getById(rateRuleId);
            Integer slowestDeliveryTime = rateRule.getSlowestDeliveryTime();
            if (nowSlowestDeliveryTime == null || slowestDeliveryTime < nowSlowestDeliveryTime) {
                nowSlowestDeliveryTime = slowestDeliveryTime;
                finalLogisticsTemplate = ltn;
            }
        }

        // 无法计算距离
        // if (finalLogisticsTemplate == null) {
        //     throw new RStatusCodeException(ZSMallStatusCodeEnum.TARGET_CITY_NOT_SUPPORT_ERROR);
        // }
        return finalLogisticsTemplate;
    }

    /**
     * 功能描述：sift物流模板v2 解决了 原版的like问题
     *
     * @param logisticsTemplateList 物流模板列表
     * @param destZipCode           Dest Zip代码
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/06/14
     */
    private String siftLogisticsTemplateV2(List<LogisticsTemplate> logisticsTemplateList,
                                         String destZipCode) throws RStatusCodeException {
        // 最终选定的物流模板
        String finalLogisticsTemplate = null;
        WorldLocation worldLocation = iWorldLocationService.queryByZipCodeV2(destZipCode);
        if (ObjectUtil.isNull(worldLocation)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.ZIP_CODE_DOES_NOT_MATCH_CITY);
        }
        Long worldLocationId = worldLocation.getId();

        // 目前最小
        Integer nowSlowestDeliveryTime = null;
        for (LogisticsTemplate logisticsTemplate : logisticsTemplateList) {
            Long logisticsTemplateId = logisticsTemplate.getId();
            String ltn = logisticsTemplate.getLogisticsTemplateNo();

            //获取关联表信息
            LogisticsRateCountryRelation relation = iLogisticsRateCountryRelationService.getByLogisticsTemplateIdAndCountryId(logisticsTemplateId, worldLocationId);
            if (ObjectUtil.isNull(relation)) {
                log.error("模板关联不存在");
                continue;
            }

            Long rateRuleId = relation.getLogisticsTemplateRateRuleId();
            LogisticsTemplateRateRule rateRule = iLogisticsTemplateRateRuleService.getById(rateRuleId);
            Integer slowestDeliveryTime = rateRule.getSlowestDeliveryTime();
            if (nowSlowestDeliveryTime == null || slowestDeliveryTime < nowSlowestDeliveryTime) {
                nowSlowestDeliveryTime = slowestDeliveryTime;
                finalLogisticsTemplate = ltn;
            }
        }

        // 无法计算距离
        // if (finalLogisticsTemplate == null) {
        //     throw new RStatusCodeException(ZSMallStatusCodeEnum.TARGET_CITY_NOT_SUPPORT_ERROR);
        // }
        return finalLogisticsTemplate;
    }

    /**
     * 更新SKU总库存
     */
    private void updateStockTotal(ShelfStateEnum spuShelfState, ShelfStateEnum skuShelfState, String productSkuCode) {
        Integer stockTotal = 0;
        if (ShelfStateEnum.OnShelf.equals(spuShelfState) && ShelfStateEnum.OnShelf.equals(skuShelfState)) {
            stockTotal = iProductSkuStockService.sumStockTotal(productSkuCode);
        }
        iProductSkuService.updateStockTotal(productSkuCode, stockTotal);
    }

    /**
     * 功能描述：获取待交付库存,邮编需要提前解密,需要在此接口内指定仓库
     *
     * @param regionCode       地区代码
     * @param productSkuCode   产品sku代码
     * @param logisticsType    物流类型
     * @param quantity         量
     * @param logisticsAccount 物流账户
     * @param postalCode       邮政编码
     * @param carrier
     * @param sTenantId
     * @param sTenantId
     * @return {@link ProductSkuStock }
     * <AUTHOR>
     * @date 2024/06/14
     */
    @Override
    public ProductSkuStock getStockForDeliver(String regionCode, String productSkuCode, LogisticsTypeEnum logisticsType,
                                              Integer quantity, Boolean logisticsAccount, String postalCode,
                                              String carrier, String dTenantId, String sTenantId) {
        ProductSkuStock productSkuStock = new ProductSkuStock();
        try {
            if (StrUtil.equals(regionCode, "US")) {
//                String finalLogisticsTemplateNo = null;

                List<LogisticsTemplate> templateList = new ArrayList<>();
                // 代发需要查询是否存在物流模板关联，若存在，则需要筛选出时间最短的
                if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                    templateList = iLogisticsTemplateService.queryAdequateStockLogisticsTemplate(productSkuCode, quantity, logisticsAccount);
//                    if (ObjectUtil.isNotEmpty(postalCode) && postalCode.contains("*")) {
//                        postalCode = postalCode.replace("*", "");
//                        finalLogisticsTemplateNo = siftLogisticsTemplateV2(templateList, postalCode);
//                    }else{
//                        if (CollUtil.isNotEmpty(templateList)) {
//                            finalLogisticsTemplateNo = siftLogisticsTemplate(templateList, postalCode);
//                        }
//                    }

                }

                // 根据物流模板找最近的仓库，并且要考虑到仓库是否支持第三方账号
                List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateStockByParams(regionCode, productSkuCode, logisticsType, quantity, null, null);
                log.info("调整库存 stockList = {}", JSONUtil.toJsonStr(stockList));
                // tag lty 库存v1
                // 自提的因为是全世界范围内选择库存，所以需要判断有没有当前国家的库存，若有则优先取当前国家的库存，没有则取其他国家的随机
                if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                    List<ProductSkuStock> currentCountry = stockList.stream().filter(stock -> stock.getCountry()
                                                                                                   .equals(regionCode))
                                                                    .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(currentCountry)) {
                        stockList = currentCountry;
                    }
                }

                if (CollUtil.size(stockList) == 1) {
                    productSkuStock = stockList.get(0);
                } else if (CollUtil.size(stockList) > 1) {
                    Boolean approvedTenant = sysTenantService.getIsApprovedTenant(dTenantId, 1);
                    String orgWarehouseCode = null;
                    boolean equals = LogisticsTypeEnum.PickUp.equals(logisticsType);
                    if(approvedTenant&&(!equals)){
                        // erp调用不再随机取库存  工具类调用,然后拿到适合的仓库号
                        List<ProductSkuStock> finalStockList = stockList;
                        DeliveryFeeByErpRequest deliveryFeeByErpRequest = TenantHelper.ignore(()->getDeliveryFeeByErpRequest(finalStockList,postalCode,productSkuCode,carrier,dTenantId ,sTenantId, regionCode)) ;
                        List<DeliveryFeeByErpResponse> deliveryFeeByErp  = null;

                        deliveryFeeByErp = deliveryFeeUtils.getDeliveryFeeByErp(Collections.singletonList(deliveryFeeByErpRequest), sTenantId);

                        orgWarehouseCode = deliveryFeeByErp.get(0).getOrgWarehouseCode();
                        LambdaQueryWrapper<Warehouse> last = new LambdaQueryWrapper<>(Warehouse.class).eq(Warehouse::getWarehouseCode, orgWarehouseCode)
                                                                                                      .eq(Warehouse::getDelFlag, 0)
                                                                                                      .eq(Warehouse::getTenantId, sTenantId)
                                                                                                      .last("limit 1");
                        Warehouse warehouse = TenantHelper.ignore(() -> iWarehouseService.getOne(last));
                        String warehouseSystemCode = warehouse.getWarehouseSystemCode();
//                    String warehouseSystemCode = "supplier718473";
                        // 遍历stockList,找到对应的元素值和warehouseSystemCode一致的元素
                        Optional<ProductSkuStock> matchingElement = stockList.stream()
                                                                             .filter(stock -> warehouseSystemCode.equals(stock.getWarehouseSystemCode()))
                                                                             .findFirst();

                        if (matchingElement.isPresent()) {
                            productSkuStock = matchingElement.get();
                            // productSkuStock is the first matching element
                        } else {
                            // No matching element found 尾程异常
                            throw new RStatusCodeException(ZSMallStatusCodeEnum.FINAL_DELIVERY_FEE_EXCEPTION);
                        }
                    }else {
                        productSkuStock = stockList.get(0);
                    }
                } else if (CollUtil.isNotEmpty(templateList)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TARGET_CITY_NOT_SUPPORT_ERROR);
                }
            } else {
                // 非美国的目的地，随机选一个非美国的仓库

                List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateStockByParams(regionCode, productSkuCode, logisticsType, quantity, null, logisticsAccount);
                if (CollUtil.isNotEmpty(stockList)) {
                    productSkuStock = stockList.get(RandomUtil.randomInt(0, CollUtil.size(stockList)));
                }
            }
        } catch (Exception e) {
            log.error("获取待交付库存出现状态码错误 = {}", e.getMessage(), e);
            productSkuStock = null;
        }
        return productSkuStock;
    }


    @Override
    public ProductSkuStock getStockForDeliver(String regionCode, String productSkuCode, LogisticsTypeEnum logisticsType,
                                              Integer quantity, Boolean logisticsAccount, String postalCode,
                                              String carrier, String dTenantId, String sTenantId,String orderNo) {
        ProductSkuStock productSkuStock = new ProductSkuStock();
        try {
            if (StrUtil.equals(regionCode, "US")) {
//                String finalLogisticsTemplateNo = null;

                List<LogisticsTemplate> templateList = new ArrayList<>();
                // 代发需要查询是否存在物流模板关联，若存在，则需要筛选出时间最短的
                if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                    templateList = iLogisticsTemplateService.queryAdequateStockLogisticsTemplate(productSkuCode, quantity, logisticsAccount);
//                    if (ObjectUtil.isNotEmpty(postalCode) && postalCode.contains("*")) {
//                        postalCode = postalCode.replace("*", "");
//                        finalLogisticsTemplateNo = siftLogisticsTemplateV2(templateList, postalCode);
//                    }else{
//                        if (CollUtil.isNotEmpty(templateList)) {
//                            finalLogisticsTemplateNo = siftLogisticsTemplate(templateList, postalCode);
//                        }
//                    }

                }

                // 根据物流模板找最近的仓库，并且要考虑到仓库是否支持第三方账号
                List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateStockByParams(regionCode, productSkuCode, logisticsType, quantity, null, null);
                log.info("调整库存 stockList = {}", JSONUtil.toJsonStr(stockList));
                // tag lty 库存v1
                // 自提的因为是全世界范围内选择库存，所以需要判断有没有当前国家的库存，若有则优先取当前国家的库存，没有则取其他国家的随机
                if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                    List<ProductSkuStock> currentCountry = stockList.stream().filter(stock -> stock.getCountry()
                                                                                                   .equals(regionCode))
                                                                    .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(currentCountry)) {
                        stockList = currentCountry;
                    }
                }

                if (CollUtil.size(stockList) == 1) {
                    productSkuStock = stockList.get(0);
                } else if (CollUtil.size(stockList) > 1) {
                    Boolean approvedTenant = sysTenantService.getIsApprovedTenant(dTenantId, 1);
                    String orgWarehouseCode = null;
                    boolean equals = LogisticsTypeEnum.PickUp.equals(logisticsType);
                    if(approvedTenant&&(!equals)){
                        //如果是OpenAPI订单且支持测算，这里先随机给一个仓库
                        Boolean b = RedisUtils.hasKey(GlobalConstants.ORDER_SOURCE + orderNo);
                        Object o = RedisUtils.getCacheObject(GlobalConstants.ORDER_SOURCE + orderNo);
                        if (b && ObjectUtil.equals(4, o)){
                            productSkuStock = stockList.get(0);
                        }else {
                            // erp调用不再随机取库存  工具类调用,然后拿到适合的仓库号
                            List<ProductSkuStock> finalStockList = stockList;
                            DeliveryFeeByErpRequest deliveryFeeByErpRequest = TenantHelper.ignore(()->getDeliveryFeeByErpRequest(finalStockList,postalCode,productSkuCode,carrier,dTenantId ,sTenantId, regionCode)) ;
                            List<DeliveryFeeByErpResponse> deliveryFeeByErp  = null;

                            deliveryFeeByErp = deliveryFeeUtils.getDeliveryFeeByErp(Collections.singletonList(deliveryFeeByErpRequest), sTenantId);

                            orgWarehouseCode = deliveryFeeByErp.get(0).getOrgWarehouseCode();
                            LambdaQueryWrapper<Warehouse> last = new LambdaQueryWrapper<>(Warehouse.class).eq(Warehouse::getWarehouseCode, orgWarehouseCode)
                                                                                                          .eq(Warehouse::getDelFlag, 0)
                                                                                                          .eq(Warehouse::getTenantId, sTenantId)
                                                                                                          .last("limit 1");
                            Warehouse warehouse = TenantHelper.ignore(() -> iWarehouseService.getOne(last));
                            String warehouseSystemCode = warehouse.getWarehouseSystemCode();
//                    String warehouseSystemCode = "supplier718473";
                            // 遍历stockList,找到对应的元素值和warehouseSystemCode一致的元素
                            Optional<ProductSkuStock> matchingElement = stockList.stream()
                                                                                 .filter(stock -> warehouseSystemCode.equals(stock.getWarehouseSystemCode()))
                                                                                 .findFirst();

                            if (matchingElement.isPresent()) {
                                productSkuStock = matchingElement.get();
                                // productSkuStock is the first matching element
                            } else {
                                // No matching element found 尾程异常
                                throw new RStatusCodeException(ZSMallStatusCodeEnum.FINAL_DELIVERY_FEE_EXCEPTION);
                            }
                        }

                    }else {
                        productSkuStock = stockList.get(0);
                    }
                } else if (CollUtil.isNotEmpty(templateList)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TARGET_CITY_NOT_SUPPORT_ERROR);
                }
            } else {
                // 非美国的目的地，随机选一个非美国的仓库

                List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateStockByParams(regionCode, productSkuCode, logisticsType, quantity, null, logisticsAccount);
                if (CollUtil.isNotEmpty(stockList)) {
                    productSkuStock = stockList.get(RandomUtil.randomInt(0, CollUtil.size(stockList)));
                }
            }
        } catch (Exception e) {
            log.error("获取待交付库存出现状态码错误 = {}", e.getMessage(), e);
            productSkuStock = null;
        }
        return productSkuStock;
    }

    @NotNull
    private DeliveryFeeByErpRequest getDeliveryFeeByErpRequest(List<ProductSkuStock> productSkuStocks, String postalCode, String productSkuCode, String logisticsCode,
                                                               String dTenantId, String sTenantId, String regionCode) {
        List<String> collect = productSkuStocks.stream().map(ProductSkuStock::getWarehouseSystemCode)
                                               .collect(Collectors.toList());
        List<String> codes = productSkuStockMapper.getWarehouseCode(collect);
        ProductSku one = iProductSkuService.getOne(Wrappers.<ProductSku>lambdaQuery()
                                                           .eq(ProductSku::getProductSkuCode, productSkuCode)
                                                           .eq(ProductSku::getDelFlag, 0));
        ProductSkuStock productSkuStock = productSkuStocks.get(0);
        DeliveryFeeByErpRequest deliveryFeeByErpRequest = new DeliveryFeeByErpRequest();
        // hengjian
        String tenantId = productSkuStock.getTenantId();
        String channelFlag = TenantHelper.ignore(()->productSkuStockMapper.getChannelFlag(dTenantId));
        deliveryFeeByErpRequest.setChannelFlag(channelFlag);
        deliveryFeeByErpRequest.setOrgWarehouseCodeList(codes);
        deliveryFeeByErpRequest.setCountryCode(regionCode);
        deliveryFeeByErpRequest.setPostcode(postalCode);
        deliveryFeeByErpRequest.setSupplierTenantId(sTenantId);
        deliveryFeeByErpRequest.setDistributorTenantId(dTenantId);
        DeliveryFeeByErpRequest.ProductItem productItem = new DeliveryFeeByErpRequest.ProductItem();
        productItem.setErpSku(one.getSku());
        productItem.setQuantity(1);

        deliveryFeeByErpRequest.setSkuList(Collections.singletonList(productItem));
        if (logisticsCode.contains(",")){
            deliveryFeeByErpRequest.setCarrierCodes(StrUtil.splitTrim(logisticsCode, ","));
        }else {
            deliveryFeeByErpRequest.setCarrierCodes(Collections.singletonList(logisticsCode));
        }
        return deliveryFeeByErpRequest;
    }

    //    @Override
//    public ProductSkuStock getStockForDeliver(String regionCode, String productSkuCode, LogisticsTypeEnum logisticsType,
//                                              Integer quantity, Boolean logisticsAccount, String postalCode) {
//        ProductSkuStock productSkuStock = new ProductSkuStock();
//        try {
//            if (StrUtil.equals(regionCode, "US")) {
//                String finalLogisticsTemplateNo = null;
//
//
//                List<LogisticsTemplate> templateList = new ArrayList<>();
//                // 代发需要查询是否存在物流模板关联，若存在，则需要筛选出时间最短的
//                if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                    templateList = iLogisticsTemplateService.queryAdequateStockLogisticsTemplate(productSkuCode, quantity, logisticsAccount);
//                    if (ObjectUtil.isNotEmpty(postalCode) && postalCode.contains("*")) {
//                        postalCode = postalCode.replace("*", "");
//                        finalLogisticsTemplateNo = siftLogisticsTemplateV2(templateList, postalCode);
//                    }else{
//                        if (CollUtil.isNotEmpty(templateList)) {
//                            finalLogisticsTemplateNo = siftLogisticsTemplate(templateList, postalCode);
//                        }
//                    }
//
//                }
//
//                // 根据物流模板找最近的仓库，并且要考虑到仓库是否支持第三方账号
//                List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateStockByParams(regionCode, productSkuCode, logisticsType, quantity, finalLogisticsTemplateNo, logisticsAccount);
//                log.info("调整库存 stockList = {}", JSONUtil.toJsonStr(stockList));
//                // tag lty 库存v1
//                // 自提的因为是全世界范围内选择库存，所以需要判断有没有当前国家的库存，若有则优先取当前国家的库存，没有则取其他国家的随机
//                if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
//                    List<ProductSkuStock> currentCountry = stockList.stream().filter(stock -> stock.getCountry()
//                                                                                                   .equals(regionCode))
//                                                                    .collect(Collectors.toList());
//                    if (CollUtil.isNotEmpty(currentCountry)) {
//                        stockList = currentCountry;
//                    }
//                }
//
//                if (CollUtil.size(stockList) == 1) {
//                    productSkuStock = stockList.get(0);
//                } else if (CollUtil.size(stockList) > 1) {
//                    productSkuStock = stockList.get(RandomUtil.randomInt(0, CollUtil.size(stockList)));
//                } else if (CollUtil.isNotEmpty(templateList)) {
//                    throw new RStatusCodeException(ZSMallStatusCodeEnum.TARGET_CITY_NOT_SUPPORT_ERROR);
//                }
//            } else {
//                // 非美国的目的地，随机选一个非美国的仓库
//
//                List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateStockByParams(regionCode, productSkuCode, logisticsType, quantity, null, logisticsAccount);
//                if (CollUtil.isNotEmpty(stockList)) {
//                    productSkuStock = stockList.get(RandomUtil.randomInt(0, CollUtil.size(stockList)));
//                }
//            }
//        } catch (Exception e) {
//            log.error("获取待交付库存出现状态码错误 = {}", e.getMessage(), e);
//            productSkuStock = null;
//        }
//        return productSkuStock;
//    }
    @Override
    public String getWarehouseSystemCode(ProductSku productSku,
                                         OrderReceiveFromThirdDTO orderReceiveFromThirdDTO,
                                         SaleOrderItemDTO orderItemDTO, Boolean aFalse) {
        SaleOrderDetailDTO saleOrderDetails = orderReceiveFromThirdDTO.getSaleOrderDetails();
        Boolean approvedTenant = sysTenantService.getIsApprovedTenant(orderReceiveFromThirdDTO.getTenantId(), 1);
        if (approvedTenant && StrUtil.isEmpty(saleOrderDetails.getCarrier())){
            List<String> carrier = new ArrayList<>();
            //如果是测算并且发货物流方式为空的话，这里进行赋值
            OrderLogisticsInfo byOrderNo =TenantHelper.ignore(()->orderLogisticsInfoService.getByOrderNo(orderReceiveFromThirdDTO.getDistributionOrderNo()));
            if (ObjectUtil.isNotNull(byOrderNo)){
                if (StrUtil.isEmpty(byOrderNo.getLogisticsCompanyName())){
                    //进行赋值
                    carrier = PriceSupportV2.getCarrier(byOrderNo.getLogisticsCompanyName(), orderReceiveFromThirdDTO.getChannelType(), aFalse, carrier);
                }else {
                    carrier=List.of(byOrderNo.getLogisticsCompanyName());
                }
            }else {
                //先不抛出异常进行赋值
                carrier= PriceSupportV2.getCarrier(null,orderReceiveFromThirdDTO.getChannelType(),aFalse,carrier);
            }
           //值用,拼接
            if (CollUtil.isNotEmpty(carrier)){
                saleOrderDetails.setCarrier(String.join(",", carrier));
            }
        }
        TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();
        List<TikTokDistrictInfo> districtInfo = address.getDistrictInfo();
        String country = null;
        String state = null;
        String federalDistrict = null;
        String county = null;
        String city = null;
        String finalZipCode = null;
        for (TikTokDistrictInfo tikTokDistrictInfo : districtInfo) {

            String levelName = tikTokDistrictInfo.getAddressLevelName();
            String addressName = tikTokDistrictInfo.getAddressName();
            if ("country".equalsIgnoreCase(levelName)) {
                country = addressName;
            }
            if ("state".equalsIgnoreCase(levelName)) {
                state = addressName;
            }
            if ("Federal District".equalsIgnoreCase(levelName)) {
                federalDistrict = addressName;
            }
            if ("county".equalsIgnoreCase(levelName)) {
                county = addressName;
            }
            if ("city".equalsIgnoreCase(levelName)) {
                city = addressName;
            }

        }
        ConfZip confZip;
        try {

            confZip = iConfZipService.getStateCodeByStateName(state);
            if(ObjectUtil.isEmpty(confZip)&&ObjectUtil.isNotEmpty(federalDistrict)){
                confZip = iConfZipService.getStateCodeByStateName(federalDistrict);
            }
            if (ObjectUtil.isEmpty(confZip)) {
                confZip = iConfZipService.getStateCodeByCity(city);
            }
        }catch (Exception e){
            log.error("获取州编码失败",e);
            confZip = null;
        }
        ProductSkuStock stock =null;
        if(ObjectUtil.isNotEmpty(confZip)){
            finalZipCode = confZip.getZip();
            stock = getStockForDeliver(address.getRegionCode(),productSku.getProductSkuCode(), LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromThirdDTO.getLogisticsType()),orderItemDTO.getQuantity(),Boolean.FALSE, finalZipCode, saleOrderDetails.getCarrier(),orderReceiveFromThirdDTO.getTenantId() , productSku.getTenantId(),orderReceiveFromThirdDTO.getOrderNo());
        }

        if (ObjectUtil.isNotEmpty(stock)){
            return stock.getWarehouseSystemCode();
        }else {
            log.error("暂无可用仓库,随机仓库发货");
            return null;
        }

    }

//    @Override
//    public HashMap<String, List<String>> getStashList(List<DeliveryFeeStock>deliveryFeeStocks) {
//        HashMap<String, List<String>> stringHashMap = new HashMap<>();
//        for (DeliveryFeeStock deliveryFeeStock : deliveryFeeStocks) {
//            LambdaQueryWrapper<ProductSkuStock> eq = new LambdaQueryWrapper<ProductSkuStock>()
//                .eq(ProductSkuStock::getProductSkuCode, deliveryFeeStock.getProductSkuCode())
//                .ge(ProductSkuStock::getStockAvailable, deliveryFeeStock.getTotalQuantity())
//                .eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid)
//                .eq(ProductSkuStock::getDelFlag, 0);
//
//            List<ProductSkuStock> list = TenantHelper.ignore(()->iProductSkuStockService.list(eq));
//            List<String> collect = list.stream().map(ProductSkuStock::getWarehouseSystemCode)
//                                       .collect(Collectors.toList());
//            if(CollUtil.isNotEmpty(collect)){
//                LambdaQueryWrapper<Warehouse> in = new LambdaQueryWrapper<Warehouse>().in(Warehouse::getWarehouseSystemCode, collect);
//                List<Warehouse> list1 = TenantHelper.ignore(()->iWarehouseService.list(in));
//                List<String> collect1 = list1.stream().map(Warehouse::getWarehouseCode)
//                                             .collect(Collectors.toList());
//                if(CollUtil.isNotEmpty(collect1)){
//                    stringHashMap.put(deliveryFeeStock.getFlag(),collect1);
//                }
//            }
//
//        }
//
//        return stringHashMap;
//    }
    @Override
    public HashMap<String, List<String>> getStashList(List<DeliveryFeeStock>deliveryFeeStocks) {
        // todo 结合仓库库存逻辑
        HashMap<String, List<String>> stringHashMap = new HashMap<>();
        for (DeliveryFeeStock deliveryFeeStock : deliveryFeeStocks) {
            LambdaQueryWrapper<ProductSkuStock> eq = new LambdaQueryWrapper<ProductSkuStock>()
                .eq(ProductSkuStock::getProductSkuCode, deliveryFeeStock.getProductSkuCode())
                .eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid)
                .eq(ProductSkuStock::getDelFlag, 0);

            List<ProductSkuStock> list = TenantHelper.ignore(()->iProductSkuStockService.list(eq));
            List<String> collect = list.stream().map(ProductSkuStock::getWarehouseSystemCode)
                                       .collect(Collectors.toList());
            if(CollUtil.isNotEmpty(collect)){
                LambdaQueryWrapper<Warehouse> in = new LambdaQueryWrapper<Warehouse>().in(Warehouse::getWarehouseSystemCode, collect);
                List<Warehouse> list1 = TenantHelper.ignore(()->iWarehouseService.list(in));
                List<String> collect1 = list1.stream().map(Warehouse::getWarehouseCode)
                                             .collect(Collectors.toList());
                if(CollUtil.isNotEmpty(collect1)){
                    stringHashMap.put(deliveryFeeStock.getFlag(),collect1);
                }
            }
        }

        return stringHashMap;
    }

    @Override
    public String adjustStockOnlyErp(AdjustStockDTO dto, String orderNo) throws StockException {
        String productSkuCode = dto.getProductSkuCode();

        try {
            Integer adjustQuantity = dto.getAdjustQuantity();
            String destCountry = dto.getDestCountry();
            String destZipCode = dto.getDestZipCode();
            String specifyWarehouse = dto.getSpecifyWarehouse();

            ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productSkuCode);
            if (adjustStockVo == null) {
                throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXISTS.args(productSkuCode));
            }
            log.info("调整库存 adjustStockVo = {}", JSONUtil.toJsonStr(adjustStockVo));

            ShelfStateEnum spuShelfState = adjustStockVo.getSpuShelfState();
            ShelfStateEnum skuShelfState = adjustStockVo.getSkuShelfState();
            Integer stockTotal = adjustStockVo.getStockTotal();
            String finalWarehouseSystemCode = null;
            ProductSkuStock finalStock = null;
            if (adjustQuantity > 0) {

                if (StrUtil.isNotBlank(specifyWarehouse)) {
                    // 需要归还至指定仓库
                    finalStock = iProductSkuStockService.queryValidByProductSkuCodeNotTenant(productSkuCode, specifyWarehouse);
                }

            } else {
                // 最终选定的库存
                // 若满足，主商品不为上架、有效，SKU不为有效其中一个，则该商品库存不足
                if (!ShelfStateEnum.OnShelf.equals(spuShelfState) || !ShelfStateEnum.OnShelf.equals(skuShelfState)) {
                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXISTS.args(productSkuCode));
                }

                if (NumberUtil.compare(adjustQuantity, stockTotal) > 0) {
                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
                }

                if (StringUtils.isNotBlank(specifyWarehouse)) {  // 指定仓库发货只会判断一次库存
                    finalStock = iProductSkuStockService.queryValidByProductSkuCodeNotTenant(productSkuCode, specifyWarehouse);
                    int absoluteAdjustQuantity = Math.abs(adjustQuantity);
                    if(ObjectUtil.isNotEmpty(finalStock)&&finalStock.getStockAvailable()<absoluteAdjustQuantity){
                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateInStock( productSkuCode, adjustQuantity);
                        finalStock = stockList.get(0);
                    }

                } else {  // 不指定仓库发货，根据物流模板绑定的仓库发货
                    // 物流方式，自提的订单，可以全世界范围内发货
                    LogisticsTypeEnum logisticsType = dto.getLogisticsType();

                    if (StrUtil.equals(destCountry, "US")) {
                        String finalLogisticsTemplateNo = null;
                        Boolean logisticsAccount = dto.getLogisticsAccount();

                        List<LogisticsTemplate> templateList = new ArrayList<>();
                        // 代发需要查询是否存在物流模板关联，若存在，则需要筛选出时间最短的
//                        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                            templateList = iLogisticsTemplateService.queryAdequateStockLogisticsTemplate(productSkuCode, adjustQuantity, logisticsAccount);
//                            if (CollUtil.isNotEmpty(templateList)) {
//                                finalLogisticsTemplateNo = siftLogisticsTemplate(templateList, destZipCode);
//                            }
//                        }

                        // 根据物流模板找最近的仓库，并且要考虑到仓库是否支持第三方账号
                        // 仓库改动:有库存就行
//                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateStockByParams(destCountry, productSkuCode, logisticsType, adjustQuantity, finalLogisticsTemplateNo, logisticsAccount);
                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateInStock( productSkuCode, adjustQuantity);

                        log.info("调整库存 stockList = {}", JSONUtil.toJsonStr(stockList));
                        // tag lty 库存v1
                        // 自提的因为是全世界范围内选择库存，所以需要判断有没有当前国家的库存，若有则优先取当前国家的库存，没有则取其他国家的随机
                        if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                            List<ProductSkuStock> currentCountry = stockList.stream().filter(stock -> stock.getCountry()
                                                                                                           .equals(destCountry))
                                                                            .collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(currentCountry)) {
                                stockList = currentCountry;
                            }
                        }

                        if (CollUtil.size(stockList) == 1) {
                            finalStock = stockList.get(0);
                        } else if (CollUtil.size(stockList) > 1) {
                            finalStock = stockList.get(RandomUtil.randomInt(0, CollUtil.size(stockList)));
                        } else if (CollUtil.isNotEmpty(templateList)) {
                            throw new RStatusCodeException(ZSMallStatusCodeEnum.TARGET_CITY_NOT_SUPPORT_ERROR);
                        }
                    } else {
                        // 非美国的目的地，随机选一个非美国的仓库
                        Boolean logisticsAccount = dto.getLogisticsAccount();
//                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateStockByParams(destCountry, productSkuCode, logisticsType, adjustQuantity, null, logisticsAccount);
                        List<ProductSkuStock> stockList = iProductSkuStockService.queryAdequateInStock( productSkuCode, adjustQuantity);
                        if (CollUtil.isNotEmpty(stockList)) {
                            finalStock = stockList.get(RandomUtil.randomInt(0, CollUtil.size(stockList)));
                        }
                    }
                }
            }

            // 选中了最终库存后，开始扣减
            if (finalStock != null) {
                Integer stockAvailable = finalStock.getStockAvailable();
                Integer newStockAvailable = NumberUtil.add(stockAvailable, adjustQuantity).intValue();
                log.info("调整库存 最终库存 = {}，新的可用库存数量 = {}", JSONUtil.toJsonStr(finalStock), newStockAvailable);

                if (newStockAvailable >= 0) {
                    finalWarehouseSystemCode = finalStock.getWarehouseSystemCode();
                    finalStock.setStockAvailable(newStockAvailable);
                    finalStock.setStockTotal(newStockAvailable);
                    iProductSkuStockService.updateByIdNotTenant(finalStock);
                } else if (adjustQuantity < 0) {  // 只有扣除库存时需要报错库存不足，归还库存时不需要报错
                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
                }
            } else if (adjustQuantity < 0) {  // 只有扣除库存时需要报错库存不足，归还库存时不需要报错
                throw new StockException(ZSMallStatusCodeEnum.NO_AVAILABLE_STOCK_FOUND.args(productSkuCode));
            }

            // 重新计算SKU总库存
            updateStockTotal(spuShelfState, skuShelfState, productSkuCode);
            if (ObjectUtil.isNotEmpty(orderNo)){
                LambdaQueryWrapper<OrderItemTrackingRecord> eq = new LambdaQueryWrapper<OrderItemTrackingRecord>().eq(OrderItemTrackingRecord::getOrderNo, orderNo)
                                                                                                                  .eq(OrderItemTrackingRecord::getDelFlag, 0);
                List<OrderItemTrackingRecord> orderItemTrackingRecords = orderItemTrackingRecordMapper.selectList(eq);

                String finalWarehouseSystemCode1 = finalWarehouseSystemCode;
                orderItemTrackingRecords.stream().forEach(orderItemTrackingRecordVo -> {
                    orderItemTrackingRecordVo.setWarehouseSystemCode(finalWarehouseSystemCode1);
                });
                try{
                    if (CollUtil.isNotEmpty(orderItemTrackingRecords)){
                        orderItemTrackingRecordMapper.updateBatchById(orderItemTrackingRecords);
                    }
                }catch (Exception e){
                    log.error("订单号[{}]更新仓库失败",orderNo,e);
                }
            }

            return finalWarehouseSystemCode;
        } catch (StockException e) {
            log.error("商品[{}]库存调整出现库存错误 = {}", productSkuCode, e.getMessage(), e);
            throw new StockException(ZSMallStatusCodeEnum.NO_AVAILABLE_STOCK_FOUND.args(productSkuCode));
        } catch (RStatusCodeException e) {
            log.error("商品[{}]库存调整出现状态码错误 = {}", productSkuCode, e.getMessage(), e);
            RStatusCodeBase statusCode = e.getStatusCode();
            throw new StockException(statusCode);
        } catch (Exception e) {
            log.error("商品[{}]库存调整出现未知错误 = {}", productSkuCode, e.getMessage(), e);
            throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_ADJUST_STOCK_ERROR.args(productSkuCode));
        }
    }

    @Override
    public void export(StockListBo bo){
        Locale headerLocale = ServletUtils.getHeaderLocale();
        String fileName = StrUtil.format(FileNameConstants.MANAGE_INVENTORY_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.ManageInventoryExport, tempFileSavePath -> {
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageNum(0);
            pageQuery.setPageSize(Integer.MAX_VALUE);
            TableDataInfo<SkuStockInfoVo> skuStockInfoVoTableDataInfo = queryProductSkuStockList(bo, pageQuery);
            List<SkuStockInfoVo> rows = skuStockInfoVoTableDataInfo.getRows();
            List<SkuStockInfoExportDTO> skuStockInfoExportDTOList = new ArrayList<>();
            for (SkuStockInfoVo skuStockInfoVo : rows) {
                SkuStockInfoExportDTO skuStockInfoExportDTO = BeanUtil.copyProperties(skuStockInfoVo, SkuStockInfoExportDTO.class);
                skuStockInfoExportDTO.setStockStateString(skuStockInfoVo.getShelfState().equals("OnShelf")? "上架" : "下架");
                skuStockInfoExportDTOList.add(skuStockInfoExportDTO);
            }
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            com.hengjian.common.excel.utils.ExcelUtil.exportExcelWithLocale(skuStockInfoExportDTOList, "manageInventory", SkuStockInfoExportDTO.class, false, outputStream, headerLocale);
            IoUtil.close(outputStream);
            return tempFile;
        });
    }

}
