package com.zsmall.warehouse.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.WarehouseAddress;
import com.zsmall.warehouse.entity.domain.bo.warehouse.WarehouseAddressBo;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseAddressVo;
import com.zsmall.warehouse.entity.mapper.WarehouseAddressMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 仓库地址信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-24
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IWarehouseAddressService extends ServiceImpl<WarehouseAddressMapper, WarehouseAddress> {

    public WarehouseAddressVo queryByWarehouseId(Long warehouseId) {
        log.info("根据仓库主表主键查询仓库地址信息 warehouseId = {}");
        LambdaQueryWrapper<WarehouseAddress> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(WarehouseAddress::getWarehouseId, warehouseId);
        return baseMapper.selectVoOne(lambdaQueryWrapper);
    }

    /**
     * 新增仓库地址信息
     */
    public Boolean insertByBo(WarehouseAddressBo bo) {
        WarehouseAddress add = MapstructUtils.convert(bo, WarehouseAddress.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改仓库地址信息
     */
    public Boolean updateByBo(WarehouseAddressBo bo) {
        WarehouseAddress update = MapstructUtils.convert(bo, WarehouseAddress.class);
        return baseMapper.updateById(update) > 0;
    }

    @InMethodLog("根据商品SKU查询所属的仓库地址")
    public List<WarehouseAddress> queryByProductSkuCode(String productSkuCode) {
        return baseMapper.queryByProductSkuCode(productSkuCode);
    }

    @InMethodLog("根据商品SKU映射渠道查询所属的仓库地址")
    public IPage<WarehouseAddress> queryByProductMappingChannel(Page<WarehouseAddress> page, Long channelId, String queryValue) {
        return baseMapper.queryByProductMappingChannel(page, channelId, queryValue);
    }

    public void cleanAndCopySourceTenantId(HashMap<String, String> warehouseSysCodeMap,
                                           HashMap<String, List<Warehouse>> sourceTargetWarehouseMap) {
        IWarehouseAddressService proxy = (IWarehouseAddressService) AopContext.currentProxy();
        List<Warehouse> warehouses = sourceTargetWarehouseMap.get("sourceWarehouseMap");
        List<Warehouse> targetWarehouses = sourceTargetWarehouseMap.get("targetWarehouseMap");
        if(CollUtil.isNotEmpty(targetWarehouses)){
            Map<Long, Long> map = targetWarehouses.stream()
                                                  .collect(Collectors.toMap(Warehouse::getMigrationId, Warehouse::getId));
            List<Long> warehouseMap = warehouses.stream().map(Warehouse::getId).collect(Collectors.toList());
            LambdaQueryWrapper<WarehouseAddress> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(WarehouseAddress::getWarehouseId,warehouseMap);
            List<WarehouseAddress> addresses = TenantHelper.ignore(() -> baseMapper.selectList(queryWrapper));
//        warehouse_id warehouse_system_code
            ArrayList<WarehouseAddress> lists = new ArrayList<>();
            for (WarehouseAddress address : addresses) {
                Long targetWarehouseId = map.get(address.getWarehouseId());
                String warehouseSystemCode = address.getWarehouseSystemCode();
                WarehouseAddress warehouseAddress = new WarehouseAddress();
                if(CollUtil.isEmpty(warehouseSysCodeMap)||(CollUtil.isNotEmpty(warehouseSysCodeMap)&&!warehouseSysCodeMap.containsKey(warehouseSystemCode))||ObjectUtil.isEmpty(targetWarehouseId)){
                    continue;
                }
                String targetWarehouseSystemCode = warehouseSysCodeMap.get(warehouseSystemCode);
                //warehouseId warehouseSystemCode
                BeanUtil.copyProperties(address,warehouseAddress);
                warehouseAddress.setId(null);
                if(ObjectUtil.isEmpty(targetWarehouseId)){
                    log.info("异常");
                }
                warehouseAddress.setWarehouseId(targetWarehouseId);
                warehouseAddress.setWarehouseSystemCode(targetWarehouseSystemCode);
//            warehouseSysCodeMap.put(sourceWarehouseCode,targetWarehouseSystemCode);

                lists.add(warehouseAddress);
            }
            proxy.saveBatch(lists);
        }

    }
}
