package com.zsmall.activity.entity.domain;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 活动库存使用记录对象 activity_use_info
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Data
@TableName("activity_use_info")
public class ActivityUseInfo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 数据记录时间
     */
    private String recordDate;

    /**
     * 分销商活动ID
     */
    private Long distributorActivityId;

    /**
     * 分销商活动编号
     */
    private String distributorActivityCode;

    /**
     * 仓库系统编号
     */
    private String warehouseSystemCode;

    /**
     * 当天剩余库存数量
     */
    private Integer inventorySurplusQuantity;

    /**
     * 当天使用库存数量
     */
    private Integer inventoryUseQuantity;

    /**
     * 创建时间
     */
    private String createTime;
}
