package com.zsmall.activity.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.activity.entity.domain.ActivityUseInfo;
import com.zsmall.activity.entity.mapper.ActivityUseInfoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


/**
 * 活动库存使用记录Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-09-08
 */
@RequiredArgsConstructor
@Service
public class IActivityUseInfoService extends ServiceImpl<ActivityUseInfoMapper, ActivityUseInfo> {

    private final ActivityUseInfoMapper baseMapper;


}
