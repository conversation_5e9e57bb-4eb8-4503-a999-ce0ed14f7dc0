<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.TenantSupSettleInReviewRecordMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.system.entity.domain.TenantSupSettleInReviewRecord">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="reviewTenantId" column="review_tenant_id" jdbcType="VARCHAR"/>
        <result property="reviewReason" column="review_reason" jdbcType="VARCHAR"/>
        <result property="reviewTime" column="review_time" jdbcType="TIMESTAMP"/>
        <result property="reviewState" column="review_state" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,review_tenant_id,
        review_reason,review_time,review_state,
        del_flag,create_dept,create_by,
        create_time,update_by,update_time
    </sql>

    <select id="getListPage" resultType="com.zsmall.system.entity.domain.dto.settleInBasic.UserSupReviewRecordDto">
        select u.tenant_id,
               u.nick_name as accountName,
               u.create_time,
               t.country,
               u.email,
               u.phonenumber AS phoneNumber,
               r.update_time,
               r.review_state,
               r.review_reason,
               t.tenant_type,
               t.third_channel_flag,
               t.company_name,
               t.license_number
        from tenant_sup_settle_in_review_record r
        join sys_user u on u.tenant_id = r.tenant_id
        join sys_tenant t on u.tenant_id = t.tenant_id
        where r.del_flag = '0' AND t.del_flag = '0'
        <if test="dto.accountName != null and dto.accountName != ''">
            AND u.nick_name like CONCAT('%', #{dto.accountName}, '%')
        </if>
        <if test="dto.email != null and dto.email != ''">
            AND u.email like CONCAT('%', #{dto.email}, '%')
        </if>
        <if test="dto.tenantId != null and dto.tenantId != ''">
            AND u.tenant_id like CONCAT('%', #{dto.tenantId}, '%')
        </if>
        <if test="null != dto.tenantType and dto.tenantType != ''">
            AND t.tenant_type = #{dto.tenantType}
        </if>
        <if test="dto.phoneNumber != null and dto.phoneNumber != ''">
            AND u.phonenumber like CONCAT('%', #{dto.phoneNumber}, '%')
        </if>
        <if test="dto.phoneNumber != null and dto.phoneNumber != ''">
            AND u.phonenumber like CONCAT('%', #{dto.phoneNumber}, '%')
        </if>
        <if test="dto.reviewState != null">
            AND r.review_state = #{dto.reviewState}
        </if>
        <if test="dto.startTime != null and dto.startTime != '' and dto.endTime != null and dto.endTime != ''">
            AND u.create_time &lt;= CONCAT(#{dto.endTime}, ' 23:59:59')
            AND u.create_time &gt;= CONCAT(#{dto.startTime}, ' 00:00:00')
        </if>
        <if test="dto.dateTime != null and dto.dateTime != ''">
            AND u.create_time &lt;= CONCAT(#{dto.dateTime}, ' 23:59:59')
            AND u.create_time &gt;= CONCAT(#{dto.dateTime}, ' 00:00:00')
        </if>
        ORDER BY r.id DESC
    </select>
</mapper>
