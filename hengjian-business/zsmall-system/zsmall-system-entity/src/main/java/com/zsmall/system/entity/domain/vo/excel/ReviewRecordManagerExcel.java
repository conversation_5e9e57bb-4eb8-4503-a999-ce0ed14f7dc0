package com.zsmall.system.entity.domain.vo.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/9/8 10:59
 */
@Data
@ExcelIgnoreUnannotated
public class ReviewRecordManagerExcel {
    @ExcelProperty(value = "租户ID")
    private String tenantId;
    /**
     * 租户类型
     */
    @ExcelProperty(value = "租户类型")
    private String tenantType;
    @ExcelProperty(value = "店铺标识")
    private String thirdChannelFlag;
    /**
     *账户名称
     */
    @ExcelProperty(value = "账号")
    private String accountName;
    /**
     *注册时间
     */
    @ExcelProperty(value = "注册时间")
    private String registeredTime;

    /**
     *国家_CN
     */
//    @ExcelProperty(value = "地区")
    private String countryCn;

    /**
     *国家
     */
    @ExcelProperty(value = "地区")
    private String country;

    /**
     *邮箱
     */
    @ExcelProperty(value = "邮箱")
    private String email;

    /**
     *电话号码
     */
    @ExcelProperty(value = "手机号")
    private String phoneNumber;
    @ExcelProperty(value = "公司名称")
    private String companyName;

    @ExcelProperty(value = "公司税务登记号")
    private String licenseNumber;
    /**
     *最后操作时间
     */
    @ExcelProperty(value = "最后操作时间")
    private String lastOperationTime;
    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String state;
    /**
     * 拒绝原因
     */
    @ExcelProperty(value = "驳回原因")
    private String reviewReason;





    /**
     *商品源类型
     */
    private String productSourceType;

    /**
     * 国家_EN
     */
    private String countryEn;
    /**
     *国家Id
     */
    private String countryId;






}
