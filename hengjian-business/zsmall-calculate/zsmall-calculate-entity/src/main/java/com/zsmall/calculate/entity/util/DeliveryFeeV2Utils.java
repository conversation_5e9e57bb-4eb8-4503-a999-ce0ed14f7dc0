package com.zsmall.calculate.entity.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.exception.base.BaseException;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.domain.SysInfEnum;
import com.hengjian.openapi.domain.vo.SysInfVo;
import com.hengjian.openapi.service.ISysInfService;
import com.zsmall.common.domain.DeliveryFeeByErpRequest;
import com.zsmall.common.domain.DeliveryFeeByErpResponse;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.mapper.ProductSkuStockMapper;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.mapper.SiteCountryCurrencyMapper;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/29 11:10
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DeliveryFeeV2Utils {
    private final ISysInfService sysInfService;
    private final IProductSkuService iProductSkuService;
    private final IWarehouseService iWarehouseService;
    private final SiteCountryCurrencyMapper siteCountryCurrencyMapper;
    private final ProductSkuStockMapper productSkuStockMapper;

    /**
     * 功能描述：
     * 调用ERP接口获取尾程派送费
     *
     * @param deliveryFeeByErpRequest 请求对象
     * @param tenantId                供应商租户id
     * @return {@link List }<{@link DeliveryFeeByErpResponse }>
     * <AUTHOR>
     * @date 2024/10/12
     */
    public List<DeliveryFeeByErpResponse> getDeliveryFeeByErp(List<DeliveryFeeByErpRequest> deliveryFeeByErpRequest,String tenantId){
        log.info(StrUtil.format("[获取ERP尾程派送费],请求参数:{}", JSONUtil.toJsonStr(deliveryFeeByErpRequest)));
        TimeInterval timer = DateUtil.timer();
        try {
            log.info("进入测算");
            //校验请求值
            validDeliveryFeeByErpRequest(deliveryFeeByErpRequest,tenantId);
            SysInfVo sysInfVo = sysInfService.queryByInfNote(SysInfEnum.GET_DELIVERY_FEE_BY_ERP);
            if (ObjectUtil.isNull(sysInfVo)){
                throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,错误原因：{} 未获取接口相关配置","GET_DELIVERY_FEE_BY_ERP"));
            }
            String url=null;
            if (sysInfVo.getIsTest()==1){
                url=sysInfVo.getInfTestUrl();
            }
            if (sysInfVo.getIsTest()==2){
                url=sysInfVo.getInfUrl();
            }
            String infParameters = sysInfVo.getInfParameters();
            JSONObject jsonObject = JSONUtil.parseObj(infParameters);
            String headerApiKey = jsonObject.getStr("x-api-key");
          //  log.error(StrUtil.format("[获取ERP尾程派送费]请求地址：{},请求Key:{}，请求参数:{}",url,headerApiKey));
            log.error(StrUtil.format("[获取ERP尾程派送费]请求参数：{}",JSONUtil.toJsonStr(deliveryFeeByErpRequest)));
            String result2 = HttpRequest.post(url)
                                        .header("x-api-key", headerApiKey)
                                        .body(JSONUtil.toJsonStr(deliveryFeeByErpRequest))
                                        .timeout(15000)//超时，毫秒
                                        .execute().body();
            log.error(StrUtil.format("[获取ERP尾程派送费]返回参数：{}",ObjectUtil.isNull(result2)? "": result2.replaceAll("\\r?\\n", "")));
            JSONObject object = JSONUtil.parseObj(result2);
            if (object.getInt("statusCode")!=200){
                log.error("[获取ERP尾程派送费] 询价失败，ERP返回：{},",result2);
                // 构筑成功返回值
                throw new BaseException("50001",StrUtil.format("[获取ERP尾程派送费] 询价失败，ERP返回：{},",ObjectUtil.isNull(result2)? "": result2.replaceAll("\\r?\\n", "")));
            }
            String jsonStr = JSONUtil.toJsonStr(object.getObj("data"));
            //转换
            List<DeliveryFeeByErpResponse> lists = com.alibaba.fastjson.JSONObject.parseArray(jsonStr, DeliveryFeeByErpResponse.class);
            //校验返回值
            validDeliveryFeeByErpResponse(deliveryFeeByErpRequest,lists);
            log.info(StrUtil.format("[获取ERP尾程派送费]耗时：{} 毫秒",timer.interval()));
            return lists ;
        }catch (Exception e){
            log.error(StrUtil.format("[获取ERP尾程派送费]失败,错误原因：{},请求参数：{}",e.getMessage(),JSONUtil.toJsonStr(deliveryFeeByErpRequest)));
            throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,错误原因：{},请求参数：{}",e.getMessage(),JSONUtil.toJsonStr(deliveryFeeByErpRequest)));
        }

    }

    /**
     /**
     * @description:  请求ERP询价接口前 校验参数
     * @author: len
     *  @date: 2024/8/4 15:50
     * @param: deliveryFeeRequests
     **/
    void validDeliveryFeeByErpRequest(List<DeliveryFeeByErpRequest>  deliveryFeeRequests,String tenantId){
        if (ObjectUtil.isNull(tenantId)){
            throw  new RuntimeException("[获取ERP尾程派送费],分销商租户不能为空!");
        }

        for (DeliveryFeeByErpRequest deliveryFeeRequest : deliveryFeeRequests) {
            if (ObjectUtil.isNull(deliveryFeeRequest.getRequestId())){
                deliveryFeeRequest.setRequestId(IdUtil.getSnowflakeNextIdStr());
            }
            if (StrUtil.isEmpty(deliveryFeeRequest.getCountryCode())){
                throw new RuntimeException("[获取ERP尾程派送费],国家编码不能为空!");
            }
            if (ObjectUtil.isEmpty(deliveryFeeRequest.getChannelFlag())){
                    throw  new RuntimeException("[获取ERP尾程派送费],分销商店铺信息不能为空!");
            }
            if ("US".equals(deliveryFeeRequest.getCountryCode()) && ObjectUtil.isEmpty(deliveryFeeRequest.getPostcode())) {
                throw new RuntimeException("[获取ERP尾程派送费], US国家测算,邮编不能为空!");
            }
            if (CollectionUtil.isEmpty(deliveryFeeRequest.getOrgWarehouseCodeList())){
                throw  new RuntimeException("[获取ERP尾程派送费],发货仓库编码不能为空");
            }
            if (CollectionUtil.isEmpty(deliveryFeeRequest.getSkuList())){
                throw new RuntimeException("[获取ERP尾程派送费],商品信息不能为空");
            }
            if (CollectionUtil.isNotEmpty(deliveryFeeRequest.getCarrierCodes())){
                List<String> carrierCodes=new ArrayList<>();
                deliveryFeeRequest.getCarrierCodes().forEach(s->{
                    if (StrUtil.isNotEmpty(s)){
                        carrierCodes.add(s);
                    }
                });
                deliveryFeeRequest.setCarrierCodes(carrierCodes);
            }
            deliveryFeeRequest.getSkuList().forEach(s->{
                if (ObjectUtil.isEmpty(s.getErpSku())){
                    throw new RuntimeException("[获取ERP尾程派送费],ErpSku编码不能为空");
                }
                //查询SKU所属的商品信息是否存在
                LambdaQueryWrapper<ProductSku> l = new LambdaQueryWrapper<>();
                l.eq(ProductSku::getSku, s.getErpSku());
                l.eq(ProductSku::getTenantId,tenantId);
                ProductSku productSku = TenantHelper.ignore(()->iProductSkuService.getOne(l));
                if (ObjectUtil.isNull(productSku)){
                    throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费],Sku:{}对应的商品信息不存在",s.getErpSku()));
                }
                if (ObjectUtil.isEmpty(s.getQuantity())){
                    throw new RuntimeException("[获取ERP尾程派送费],ErpSku商品数量不能为空");
                }else{
                    s.setQuantity(1);
                }
                if (s.getQuantity()==0){
                    throw new RuntimeException("[获取ERP尾程派送费],ErpSku商品数量不能为0");
                }
                //查询商品有库存/不是单仓/发货仓库国家支持配送
                List<ProductSkuStock> productSkuStocks =TenantHelper.ignore(()->productSkuStockMapper.queryDropShippingStockAvailable(productSku.getProductSkuCode(),
                    s.getQuantity(),deliveryFeeRequest.getSupplierTenantId(),deliveryFeeRequest.getCountryCode()));
                if (CollectionUtil.isEmpty(productSkuStocks)){
                    throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费],获取Sku:{}单仓信息 失败,请求仓库编码：{}，实际仓库编码：{}", s.getErpSku(), JSONUtil.toJsonStr(deliveryFeeRequest.getOrgWarehouseCodeList()), JSONUtil.toJsonStr(productSkuStocks)));
                }else {
                    //判断数据库里面的仓库是不是包含请求的仓库
                    List<String> collect = productSkuStocks.stream().map(ProductSkuStock::getWarehouseSystemCode)
                                                           .collect(Collectors.toList());
                    if (!new HashSet<>(collect).containsAll(deliveryFeeRequest.getOrgWarehouseCodeList())) {
                        //使用实际查询的仓库去覆盖入参仓库，临时方案，后续入参仓库需要上游适配单仓逻辑，只做校验
                        deliveryFeeRequest.setOrgWarehouseCodeList(collect);
                        //throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费],Sku:{}仓库信息错误,请求仓库编码：{}，实际仓库编码：{}", s.getErpSku(), JSONUtil.toJsonStr(deliveryFeeRequest.getOrgWarehouseCodeList()), JSONUtil.toJsonStr(collect)));
                    }
                }
            });
        }
    }



    /**
     * @description:  ERP询价结果校验
     * @author: len
     *  @date: 2024/8/4 15:50
     * @param: requests 询价请求参数
     * @param: response  询价返回参数
     **/
    void validDeliveryFeeByErpResponse(List<DeliveryFeeByErpRequest> requests, List<DeliveryFeeByErpResponse> response) {
        //判断
        if (CollectionUtil.isEmpty(response)) {
            throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回结果为空，返回参数：{}", JSONUtil.toJsonStr(response)));
        }
        //如果询价前后数量不对，抛出异常
        if (requests.size() != response.size()) {
            throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价前后数量不对，请求参数：{}，返回参数：{}", JSONUtil.toJsonStr(requests), JSONUtil.toJsonStr(response)));
        }
        response.forEach(s -> {
            if (ObjectUtil.isEmpty(s.getOrgWarehouseCode())) {
                throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回仓库编码为空，返回参数：{}", JSONUtil.toJsonStr(s)));
            }
            if (CollectionUtil.isEmpty(s.getSkuList())) {
                throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回商品信息为空，返回参数：{}", JSONUtil.toJsonStr(s)));
            }
            if (ObjectUtil.isEmpty(s.getShippingFee())) {
                throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回运费为空，返回参数：{}", JSONUtil.toJsonStr(s)));
            }
            if (StrUtil.isEmpty(s.getCurrencyCode())){
                throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回货币符号为空，返回参数：{}", JSONUtil.toJsonStr(s)));
            }
            //判断ERP返回的币种是否正确
            LambdaQueryWrapper<SiteCountryCurrency> sc = new LambdaQueryWrapper<>();
            sc.eq(SiteCountryCurrency::getCurrencyCode, s.getCurrencyCode());
            sc.eq(SiteCountryCurrency::getDelFlag,0);
            sc.groupBy(SiteCountryCurrency::getCurrencyCode);
            SiteCountryCurrency siteCountryCurrency = siteCountryCurrencyMapper.selectOne(sc);
            if (ObjectUtil.isNull(siteCountryCurrency)){
                throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回货币符号不匹配，返回参数：{}", JSONUtil.toJsonStr(s)));
            }
            //校验返回的商品信息
            for (DeliveryFeeByErpResponse.ProductItem productItem : s.getSkuList()) {
                String sku =productItem.getErpSku();
                if (ObjectUtil.isEmpty(sku)){
                    throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回商品信息为空，返回参数：{}", JSONUtil.toJsonStr(s)));
                }
                LambdaQueryWrapper<ProductSku> p = new LambdaQueryWrapper<>();
                p.eq(ProductSku::getSku, sku);
                p.eq(ProductSku::getDelFlag,0);
                p.eq(ProductSku::getShelfState, ShelfStateEnum.OnShelf.name());
                p.eq(ProductSku::getTenantId, requests.get(0).getSupplierTenantId());
                ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getOne(p));
                if (ObjectUtil.isNull(productSku)) {
                    throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回SKU信息分销系统不存在，返回参数：{}", JSONUtil.toJsonStr(s)));
                }
                if (productItem.getQuantity()==0){
                    throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回SKU数量为0，返回参数：{}", JSONUtil.toJsonStr(s)));
                }
                //判断返回的仓库信息在分销系统是否存在
                LambdaQueryWrapper<Warehouse> q = new LambdaQueryWrapper<>();
                q.eq(Warehouse::getWarehouseCode, s.getOrgWarehouseCode());
                q.eq(Warehouse::getWarehouseState, 1);
                q.eq(Warehouse::getTenantId, productSku.getTenantId());
                Warehouse one = TenantHelper.ignore(() -> iWarehouseService.getOne(q));
                if (ObjectUtil.isNull(one)) {
                    throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，返回的仓库编码分销系统不存在，租户ID:{},返回参数：{}", LoginHelper.getTenantId(), JSONUtil.toJsonStr(s)));
                }
            }
            Map<String, DeliveryFeeByErpRequest> collect = requests.stream()
                                                                   .collect(Collectors.toMap(
                                                                       DeliveryFeeByErpRequest::getRequestId,
                                                                       request -> request,
                                                                       (existing, replacement) -> replacement
                                                                   ));
            //         校验询价前后，ERP返回的商品数量是否一致
            DeliveryFeeByErpRequest deliveryFeeByErpRequest = collect.get(s.getRequestId());
            if (ObjectUtil.isEmpty(deliveryFeeByErpRequest)) {
                throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价前后requestId不能为空，返回参数：{}", JSONUtil.toJsonStr(s)));
            }
            if (deliveryFeeByErpRequest.getSkuList().size() != s.getSkuList().size()) {
                throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价前后商品数量不对，请求参数：{}，返回参数：{}", JSONUtil.toJsonStr(deliveryFeeByErpRequest), JSONUtil.toJsonStr(s)));
            }
        });
        //获取返回的EepSku信息
        List<String> responseRrpSkus = response.stream().map(DeliveryFeeByErpResponse::getSkuList).filter(Objects::nonNull).flatMap(List::stream)
                                               .map(DeliveryFeeByErpResponse.ProductItem::getErpSku)
                                               .collect(Collectors.toList());
        //获取返回的EepSku信息
        List<String> requestRrpSkus = requests.stream().map(DeliveryFeeByErpRequest::getSkuList).flatMap(List::stream)
                                              .map(DeliveryFeeByErpRequest.ProductItem::getErpSku)
                                              .collect(Collectors.toList());
        if (!responseRrpSkus.equals(requestRrpSkus)) {
            throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价前后erpSku数据异常，请求参数：{}，返回参数：{}", JSONUtil.toJsonStr(requests), JSONUtil.toJsonStr(response)));
        }
    }

    /**
     * @description:  根据productSkuCode查询商品有库存的发货仓信息
     * @author: len
     *  @date: 2024/8/4 14:45
     * @param: sku  商品编码
     * @return: java.util.List<java.lang.String> 有库存仓库集合
     **/
    public List<String>  getInventoryWarehouseBySku(String productSkuCode){
        //查询当前商品有库存的发货仓信息
        List<String> productSkuStock = TenantHelper.ignore(() -> iProductSkuService.getProductSkuStockBySku(productSkuCode, 1));
        if (CollectionUtil.isEmpty(productSkuStock)) {
            throw new RuntimeException(StrUtil.format("当前商品:{} 所有仓库无库存", productSkuCode));
        }
        return productSkuStock;
    }

}
